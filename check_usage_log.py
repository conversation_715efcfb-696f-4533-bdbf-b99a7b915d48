#!/usr/bin/env python3
"""
Check the usage log file for credit deductions
"""
import json
import os

def check_usage_log():
    """Check the usage log file"""
    usage_log_path = 'data/usage_log.json'
    
    print("="*60)
    print("CHECKING USAGE LOG FILE")
    print("="*60)
    
    if not os.path.exists(usage_log_path):
        print(f"Usage log file not found at: {usage_log_path}")
        return
    
    try:
        with open(usage_log_path, 'r') as f:
            usage_log = json.load(f)
        
        print(f"Total usage: {usage_log.get('total_usage', 0)} credits")
        print(f"Last updated: {usage_log.get('last_updated', 0)}")
        
        usage_entries = usage_log.get('usage', [])
        print(f"Usage entries: {len(usage_entries)}")
        
        if usage_entries:
            print("\nRecent usage entries:")
            for i, entry in enumerate(usage_entries[-10:]):  # Last 10 entries
                credits = entry.get('credits_used', 0)
                game_id = entry.get('game_id', 'N/A')
                share = entry.get('share_percentage', 0)
                timestamp = entry.get('timestamp', 0)
                print(f"  {i+1}. {credits} credits - Game:{game_id} - Share:{share}% - Time:{timestamp}")
        else:
            print("No usage entries found")
            
    except Exception as e:
        print(f"Error reading usage log: {e}")

def compare_usage_vs_stats():
    """Compare usage log entries with stats records"""
    print("\n" + "="*60)
    print("COMPARING USAGE LOG VS STATS RECORDS")
    print("="*60)
    
    # Get usage log count
    usage_count = 0
    usage_log_path = 'data/usage_log.json'
    
    if os.path.exists(usage_log_path):
        try:
            with open(usage_log_path, 'r') as f:
                usage_log = json.load(f)
            usage_entries = usage_log.get('usage', [])
            # Count entries with actual credit usage
            usage_count = len([e for e in usage_entries if e.get('credits_used', 0) > 0])
        except:
            pass
    
    # Get meaningful stats count
    stats_count = 0
    stats_db_path = 'data/stats.db'
    
    if os.path.exists(stats_db_path):
        try:
            import sqlite3
            conn = sqlite3.connect(stats_db_path)
            cursor = conn.cursor()
            cursor.execute('''
            SELECT COUNT(*) FROM game_history
            WHERE (
                total_calls > 0
                AND username NOT LIKE '%Demo%'
                AND NOT (username = 'Game Reset' AND total_calls = 0)
                AND players > 0
            )
            ''')
            stats_count = cursor.fetchone()[0]
            conn.close()
        except:
            pass
    
    print(f"Credit usage entries (credits_used > 0): {usage_count}")
    print(f"Meaningful game records: {stats_count}")
    print(f"Difference: {abs(usage_count - stats_count)}")
    
    if usage_count == stats_count:
        print("✅ Perfect synchronization!")
    elif usage_count > stats_count:
        print(f"❌ {usage_count - stats_count} credit deductions missing corresponding game records")
        print("   → Need to create game records for credit deductions")
    else:
        print(f"❌ {stats_count - usage_count} game records without corresponding credit deductions")
        print("   → Need to create credit deductions for game records")

def identify_integration_strategy():
    """Identify the best integration strategy"""
    print("\n" + "="*60)
    print("INTEGRATION STRATEGY")
    print("="*60)
    
    print("CURRENT SITUATION:")
    print("  • Credit deductions logged to: data/usage_log.json")
    print("  • Game records stored in: data/stats.db")
    print("  • No direct connection between the two systems")
    
    print("\nREQUIRED INTEGRATION:")
    print("  1. Hook into usage_tracker.end_game() method")
    print("  2. When credits are deducted, immediately record game in stats")
    print("  3. Use credit deduction data to populate game record")
    print("  4. Ensure every credit deduction = one game record")
    
    print("\nIMPLEMENTATION PLAN:")
    print("  ✅ Modify usage_tracker.end_game() to call stats recording")
    print("  ✅ Pass credit deduction data to stats system")
    print("  ✅ Create game records based on credit transactions")
    print("  ✅ Validate synchronization between systems")

if __name__ == '__main__':
    check_usage_log()
    compare_usage_vs_stats()
    identify_integration_strategy()
