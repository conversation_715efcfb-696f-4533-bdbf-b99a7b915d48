#!/usr/bin/env python3
"""
Script to test the game recording improvements
"""
import sys
import os
import time

def test_game_recording_logic():
    """Test the improved game recording logic"""
    print("Testing game recording logic...")
    
    try:
        # Create a mock game object to test the recording logic
        class MockGame:
            def __init__(self):
                self.called_numbers = [1, 15, 23, 45, 67, 78, 89, 12, 34, 56]  # 10 numbers
                self.players = ['Player1', 'Player2', 'Player3']  # 3 players
                self.game_started = True
                self.is_demo_mode = False
                self.prize_pool = 150.0
                self.bet_amount = 25.0
        
        # Create a mock game state handler
        class MockGameStateHandler:
            def __init__(self, game):
                self.game = game
                self.show_winner_display = False
                self.validation_result = None
                self.player_claim_cartella = None
                
            def _get_current_commission_percentage(self):
                return 20.0
        
        # Test the logic
        game = MockGame()
        handler = MockGameStateHandler(game)
        
        # Test conditions from the improved logic
        is_demo_mode = getattr(game, 'is_demo_mode', False)
        
        print(f"Game state:")
        print(f"  Called numbers: {len(game.called_numbers)}")
        print(f"  Players: {len(game.players)}")
        print(f"  Game started: {game.game_started}")
        print(f"  Demo mode: {is_demo_mode}")
        print(f"  Show winner display: {handler.show_winner_display}")
        print(f"  Validation result: {handler.validation_result}")
        print(f"  Player claim cartella: {handler.player_claim_cartella}")
        
        # Test the recording conditions
        should_record_game = False
        game_completion_type = "unknown"
        winner_name = "Unknown"

        # Check if we have a valid claim and stats tracking is enabled
        if handler.show_winner_display and handler.validation_result and handler.player_claim_cartella:
            should_record_game = True
            game_completion_type = "valid_winner"
            winner_name = f"Cartella #{handler.player_claim_cartella}"
            print("✅ Would record as: valid_winner")

        # ENHANCED: Record games with meaningful activity (numbers called and players present)
        elif (hasattr(game, 'called_numbers') and len(game.called_numbers) > 0 and
              hasattr(game, 'players') and len(game.players) > 0 and
              not is_demo_mode):
            should_record_game = True
            game_completion_type = "completed_no_claim"
            winner_name = "No Winner"
            print("✅ Would record as: completed_no_claim")

        # ENHANCED: Also record games that were started but ended early (with some activity)
        elif (hasattr(game, 'called_numbers') and len(game.called_numbers) >= 5 and
              hasattr(game, 'game_started') and game.game_started and
              not is_demo_mode):
            should_record_game = True
            game_completion_type = "ended_early"
            winner_name = "Game Ended Early"
            print("✅ Would record as: ended_early")
        
        print(f"\nRecording decision:")
        print(f"  Should record: {should_record_game}")
        print(f"  Completion type: {game_completion_type}")
        print(f"  Winner name: {winner_name}")
        
        if should_record_game:
            # Create game data for statistics
            game_data = {
                "winner_name": winner_name,
                "winner_cartella": handler.player_claim_cartella if hasattr(handler, 'player_claim_cartella') else 0,
                "claim_type": game_completion_type,
                "game_duration": 300,  # 5 minutes
                "player_count": len(game.players),
                "prize_amount": getattr(game, 'prize_pool', 0),
                "commission_percentage": handler._get_current_commission_percentage(),
                "called_numbers": game.called_numbers,
                "is_demo_mode": is_demo_mode,
                "date_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                "stake": getattr(game, 'bet_amount', 25),
                "bet_amount": getattr(game, 'bet_amount', 25),
                "completion_type": game_completion_type
            }
            
            print(f"\nGame data that would be recorded:")
            for key, value in game_data.items():
                if key == 'called_numbers':
                    print(f"  {key}: {len(value)} numbers")
                else:
                    print(f"  {key}: {value}")
            
            print("\n✅ This game would be properly recorded!")
        else:
            print("\n❌ This game would NOT be recorded")
            
    except Exception as e:
        print(f"Error testing game recording logic: {e}")
        import traceback
        traceback.print_exc()

def test_reset_recording_logic():
    """Test the improved reset recording logic"""
    print("\n" + "="*50)
    print("Testing reset recording logic...")
    
    # Test case 1: Minimal activity (should NOT record)
    print("\nTest case 1: Minimal activity")
    called_numbers = []
    player_count = 0
    game_started = False
    
    if (len(called_numbers) == 0 or player_count == 0 or not game_started or 
        len(called_numbers) < 5):
        print("✅ Would NOT record reset - insufficient activity")
    else:
        print("❌ Would record reset - this is wrong")
    
    # Test case 2: Some activity but not enough (should NOT record)
    print("\nTest case 2: Some activity but not enough")
    called_numbers = [1, 2, 3]  # Only 3 numbers
    player_count = 2
    game_started = True
    
    if (len(called_numbers) == 0 or player_count == 0 or not game_started or 
        len(called_numbers) < 5):
        print("✅ Would NOT record reset - insufficient activity (< 5 numbers)")
    else:
        print("❌ Would record reset - this is wrong")
    
    # Test case 3: Substantial activity (should record)
    print("\nTest case 3: Substantial activity")
    called_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # 12 numbers
    player_count = 3
    game_started = True
    
    if (len(called_numbers) == 0 or player_count == 0 or not game_started or 
        len(called_numbers) < 5):
        print("❌ Would NOT record reset - this is wrong")
    elif len(called_numbers) >= 10 and player_count >= 2:
        print("✅ Would record reset - substantial activity detected")
    else:
        print("⚠️ Would not record reset - borderline case")

if __name__ == '__main__':
    test_game_recording_logic()
    test_reset_recording_logic()
