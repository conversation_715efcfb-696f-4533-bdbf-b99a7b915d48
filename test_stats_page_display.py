#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to test the stats page display with filtered data
"""
import sys
import os

def test_stats_page_data_loading():
    """Test that the stats page loads filtered data correctly"""
    print("Testing stats page data loading...")
    
    try:
        # Test the stats page data loading without actually opening the GUI
        import pygame
        pygame.init()
        
        # Create a minimal screen for testing
        screen = pygame.display.set_mode((800, 600))
        
        # Import and create stats page
        from stats_page import StatsPage
        
        print("Creating StatsPage instance...")
        stats_page = StatsPage(screen)
        
        # Wait a moment for background loading
        import time
        time.sleep(2)
        
        # Check if game history was loaded
        if hasattr(stats_page, 'game_history'):
            print(f"Game history loaded: {len(stats_page.game_history)} records")
            
            if stats_page.game_history:
                print("Game history records:")
                for i, game in enumerate(stats_page.game_history):
                    username = game.get('username', 'Unknown')
                    total_calls = game.get('total_calls', 0)
                    players = game.get('players', 0)
                    status = game.get('status', 'Unknown')
                    print(f"  {i+1}. {username} - {total_calls} calls - {players} players - {status}")
                
                # Verify filtering worked
                reset_entries = [g for g in stats_page.game_history if g.get('username') == 'Game Reset' and g.get('total_calls', 0) == 0]
                demo_entries = [g for g in stats_page.game_history if 'Demo' in g.get('username', '')]
                zero_call_entries = [g for g in stats_page.game_history if g.get('total_calls', 0) == 0]
                
                if not reset_entries and not demo_entries and not zero_call_entries:
                    print("✅ Stats page filtering working correctly!")
                else:
                    print(f"⚠️ Filtering issues: {len(reset_entries)} reset, {len(demo_entries)} demo, {len(zero_call_entries)} zero-call entries")
            else:
                print("No game history loaded")
        else:
            print("Game history attribute not found")
        
        # Check other stats
        if hasattr(stats_page, 'total_earnings'):
            print(f"Total earnings: {stats_page.total_earnings}")
        
        if hasattr(stats_page, 'daily_earnings'):
            print(f"Daily earnings: {stats_page.daily_earnings}")
        
        if hasattr(stats_page, 'daily_games'):
            print(f"Daily games: {stats_page.daily_games}")
        
        pygame.quit()
        print("✅ Stats page test completed successfully")
        
    except Exception as e:
        print(f"Error testing stats page: {e}")
        import traceback
        traceback.print_exc()
        try:
            pygame.quit()
        except:
            pass

def test_html_export_filtering():
    """Test that HTML export also uses filtered data"""
    print("\n" + "="*50)
    print("Testing HTML export filtering...")
    
    try:
        # Test the export functionality
        from stats_page import StatsPage
        import pygame
        
        pygame.init()
        screen = pygame.display.set_mode((800, 600))
        stats_page = StatsPage(screen)
        
        # Wait for data loading
        import time
        time.sleep(2)
        
        # Test export data collection
        if hasattr(stats_page, 'game_history') and stats_page.game_history:
            export_data = {
                'game_history': stats_page.game_history.copy(),
                'metadata': {
                    'game_history_count': len(stats_page.game_history)
                }
            }
            
            print(f"Export would include {len(export_data['game_history'])} game records")
            
            # Check if filtered data is being exported
            reset_entries = [g for g in export_data['game_history'] if g.get('username') == 'Game Reset' and g.get('total_calls', 0) == 0]
            if not reset_entries:
                print("✅ HTML export filtering working correctly!")
            else:
                print(f"⚠️ HTML export includes {len(reset_entries)} meaningless reset entries")
        else:
            print("No game history available for export test")
        
        pygame.quit()
        
    except Exception as e:
        print(f"Error testing HTML export: {e}")
        import traceback
        traceback.print_exc()
        try:
            pygame.quit()
        except:
            pass

if __name__ == '__main__':
    test_stats_page_data_loading()
    test_html_export_filtering()
