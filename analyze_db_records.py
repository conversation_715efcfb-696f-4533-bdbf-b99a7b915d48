#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze the database records and identify issues
"""
import sqlite3
import os
import json

def analyze_database():
    """Analyze the database records to identify issues"""
    db_path = 'data/stats.db'
    
    if not os.path.exists(db_path):
        print(f'Database does not exist at: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get total count
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_count = cursor.fetchone()[0]
        print(f'Total game history records: {total_count}')
        
        # Count by username patterns
        cursor.execute('''
        SELECT 
            CASE 
                WHEN username LIKE '%Game Reset%' THEN 'Game Reset'
                WHEN username LIKE '%Demo%' THEN 'Demo'
                WHEN username = 'Unknown' THEN 'Unknown'
                WHEN username = 'No Winner' THEN 'No Winner'
                ELSE 'Real Player'
            END as user_type,
            COUNT(*) as count
        FROM game_history 
        GROUP BY user_type
        ORDER BY count DESC
        ''')
        
        user_types = cursor.fetchall()
        print('\nRecords by user type:')
        for user_type, count in user_types:
            print(f'  {user_type}: {count}')
        
        # Count by status
        cursor.execute('''
        SELECT status, COUNT(*) as count
        FROM game_history 
        GROUP BY status
        ORDER BY count DESC
        ''')
        
        statuses = cursor.fetchall()
        print('\nRecords by status:')
        for status, count in statuses:
            print(f'  {status}: {count}')
        
        # Count by total_calls
        cursor.execute('''
        SELECT 
            CASE 
                WHEN total_calls = 0 THEN 'No calls (0)'
                WHEN total_calls > 0 AND total_calls <= 10 THEN 'Few calls (1-10)'
                WHEN total_calls > 10 AND total_calls <= 30 THEN 'Normal calls (11-30)'
                WHEN total_calls > 30 THEN 'Many calls (30+)'
            END as call_range,
            COUNT(*) as count
        FROM game_history 
        GROUP BY call_range
        ORDER BY count DESC
        ''')
        
        call_ranges = cursor.fetchall()
        print('\nRecords by total calls:')
        for call_range, count in call_ranges:
            print(f'  {call_range}: {count}')
        
        # Find real games (non-reset, non-demo, with actual calls)
        cursor.execute('''
        SELECT id, date_time, username, players, total_calls, status, stake, total_prize
        FROM game_history 
        WHERE username NOT LIKE '%Game Reset%' 
        AND username NOT LIKE '%Demo%'
        AND total_calls > 0
        ORDER BY date_time DESC
        LIMIT 10
        ''')
        
        real_games = cursor.fetchall()
        print(f'\nReal games found: {len(real_games)}')
        if real_games:
            print('Recent real games:')
            for game in real_games:
                print(f'  ID {game[0]}: {game[1]} - {game[2]} - {game[3]} players - {game[4]} calls - {game[5]} - {game[6]} ETB stake')
        else:
            print('No real games found!')
        
        # Check recent entries
        cursor.execute('''
        SELECT id, date_time, username, players, total_calls, status, details
        FROM game_history 
        ORDER BY id DESC
        LIMIT 5
        ''')
        
        recent_entries = cursor.fetchall()
        print('\nMost recent entries:')
        for entry in recent_entries:
            details = entry[6]
            if details:
                try:
                    details_obj = json.loads(details)
                    is_demo = details_obj.get('is_demo_mode', False)
                    claim_type = details_obj.get('claim_type', 'Unknown')
                    print(f'  ID {entry[0]}: {entry[1]} - {entry[2]} - Demo: {is_demo} - Claim: {claim_type}')
                except:
                    print(f'  ID {entry[0]}: {entry[1]} - {entry[2]} - Invalid details JSON')
            else:
                print(f'  ID {entry[0]}: {entry[1]} - {entry[2]} - No details')
        
        conn.close()
        
    except Exception as e:
        print(f'Error analyzing database: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    analyze_database()
