2025-06-13 13:07:08.548 - ensure_database_exists called from thread 8616
2025-06-13 13:07:08.549 - Creating new thread-specific database connection to data\stats.db for thread 8616
2025-06-13 13:07:08.550 - New database connection created successfully for thread 8616
2025-06-13 13:07:08.558 - Stats database initialized successfully
2025-06-13 13:07:08.559 - ensure_database_exists called from thread 8616
2025-06-13 13:07:08.560 - Using existing connection for thread 8616
2025-06-13 13:07:08.561 - Stats database initialized successfully
2025-06-13 13:07:08.562 - ensure_database_exists called from thread 8616
2025-06-13 13:07:08.563 - Using existing connection for thread 8616
2025-06-13 13:07:08.565 - Stats database initialized successfully
2025-06-13 13:12:20.148 - ensure_database_exists called from thread 11216
2025-06-13 13:12:20.149 - Creating new thread-specific database connection to data\stats.db for thread 11216
2025-06-13 13:12:20.149 - New database connection created successfully for thread 11216
2025-06-13 13:12:20.151 - Stats database initialized successfully
2025-06-13 13:12:20.152 - ensure_database_exists called from thread 11216
2025-06-13 13:12:20.153 - Using existing connection for thread 11216
2025-06-13 13:12:20.153 - Stats database initialized successfully
2025-06-13 13:12:20.154 - ensure_database_exists called from thread 11216
2025-06-13 13:12:20.154 - Using existing connection for thread 11216
2025-06-13 13:12:20.155 - Stats database initialized successfully
2025-06-13 13:13:29.430 - Using existing connection for thread 11216
2025-06-13 13:13:29.514 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:13:29.515 - New database connection created successfully for thread 18356
2025-06-13 13:13:29.628 - Database connection closed for thread 18356
2025-06-13 13:13:29.628 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:13:29.629 - New database connection created successfully for thread 18356
2025-06-13 13:13:29.634 - Database connection closed for thread 18356
2025-06-13 13:13:29.642 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:13:29.644 - New database connection created successfully for thread 18356
2025-06-13 13:13:29.654 - get_summary_stats called from thread 18356
2025-06-13 13:13:29.654 - Using existing connection for thread 18356
2025-06-13 13:13:29.657 - Total earnings from database: 0.0
2025-06-13 13:13:29.660 - Daily earnings from database: 0.0
2025-06-13 13:13:29.661 - Daily games from database: 0
2025-06-13 13:13:29.662 - Wallet balance from database: 0
2025-06-13 13:13:29.663 - Total games played from database: 0
2025-06-13 13:13:29.663 - Total winners from database: 0
2025-06-13 13:13:29.664 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-13 13:14:00.611 - Using existing connection for thread 18356
2025-06-13 13:14:00.668 - Database connection closed for thread 18356
2025-06-13 13:14:00.670 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.671 - New database connection created successfully for thread 18356
2025-06-13 13:14:00.671 - Database connection closed for thread 18356
2025-06-13 13:14:00.671 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.672 - New database connection created successfully for thread 18356
2025-06-13 13:14:00.674 - Database connection closed for thread 18356
2025-06-13 13:14:00.674 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.675 - New database connection created successfully for thread 18356
2025-06-13 13:14:00.680 - Database connection closed for thread 18356
2025-06-13 13:14:00.682 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.682 - New database connection created successfully for thread 18356
2025-06-13 13:14:00.686 - Database connection closed for thread 18356
2025-06-13 13:14:00.687 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.688 - New database connection created successfully for thread 18356
2025-06-13 13:14:00.701 - Database connection closed for thread 18356
2025-06-13 13:14:00.702 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.702 - New database connection created successfully for thread 18356
2025-06-13 13:14:00.704 - Database connection closed for thread 18356
2025-06-13 13:14:00.704 - Creating new thread-specific database connection to data\stats.db for thread 18356
2025-06-13 13:14:00.706 - New database connection created successfully for thread 18356
2025-06-13 13:15:13.259 - ensure_database_exists called from thread 12664
2025-06-13 13:15:13.259 - Creating new thread-specific database connection to data\stats.db for thread 12664
2025-06-13 13:15:13.260 - New database connection created successfully for thread 12664
2025-06-13 13:15:13.262 - Stats database initialized successfully
2025-06-13 13:15:13.263 - ensure_database_exists called from thread 12664
2025-06-13 13:15:13.264 - Using existing connection for thread 12664
2025-06-13 13:15:13.266 - Stats database initialized successfully
2025-06-13 13:15:13.266 - ensure_database_exists called from thread 12664
2025-06-13 13:15:13.266 - Using existing connection for thread 12664
2025-06-13 13:15:13.266 - Stats database initialized successfully
2025-07-16 17:02:27.863 - ensure_database_exists called from thread 16812
2025-07-16 17:02:27.864 - Creating new thread-specific database connection to data\stats.db for thread 16812
2025-07-16 17:02:27.866 - New database connection created successfully for thread 16812
2025-07-16 17:02:27.870 - Stats database initialized successfully
2025-07-16 17:02:27.871 - ensure_database_exists called from thread 16812
2025-07-16 17:02:27.872 - Using existing connection for thread 16812
2025-07-16 17:02:27.872 - Stats database initialized successfully
2025-07-16 17:02:27.873 - ensure_database_exists called from thread 16812
2025-07-16 17:02:27.874 - Using existing connection for thread 16812
2025-07-16 17:02:27.874 - Stats database initialized successfully
2025-07-16 17:04:50.258 - Using existing connection for thread 16812
2025-07-16 17:04:50.320 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:04:50.326 - New database connection created successfully for thread 17816
2025-07-16 17:04:50.470 - Database connection closed for thread 17816
2025-07-16 17:04:50.471 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:04:50.472 - New database connection created successfully for thread 17816
2025-07-16 17:04:50.490 - Database connection closed for thread 17816
2025-07-16 17:04:50.493 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:04:50.496 - New database connection created successfully for thread 17816
2025-07-16 17:04:50.526 - get_summary_stats called from thread 17816
2025-07-16 17:04:50.528 - Using existing connection for thread 17816
2025-07-16 17:04:50.535 - Total earnings from database: 6.0
2025-07-16 17:04:50.542 - Daily earnings from database: 0.0
2025-07-16 17:04:50.548 - Daily games from database: 0
2025-07-16 17:04:50.549 - Wallet balance from database: 0
2025-07-16 17:04:50.553 - Total games played from database: 1
2025-07-16 17:04:50.553 - Total winners from database: 1
2025-07-16 17:04:50.559 - Returning summary stats: {'total_earnings': 6.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:12:39.288 - Recreating expired connection for thread 17816
2025-07-16 17:12:39.327 - Database connection closed for thread 17816
2025-07-16 17:12:39.342 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.343 - New database connection created successfully for thread 17816
2025-07-16 17:12:39.344 - Database connection closed for thread 17816
2025-07-16 17:12:39.346 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.347 - New database connection created successfully for thread 17816
2025-07-16 17:12:39.351 - Database connection closed for thread 17816
2025-07-16 17:12:39.352 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.353 - New database connection created successfully for thread 17816
2025-07-16 17:12:39.362 - Database connection closed for thread 17816
2025-07-16 17:12:39.364 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.365 - New database connection created successfully for thread 17816
2025-07-16 17:12:39.367 - Database connection closed for thread 17816
2025-07-16 17:12:39.368 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.369 - New database connection created successfully for thread 17816
2025-07-16 17:12:39.388 - Database connection closed for thread 17816
2025-07-16 17:12:39.390 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.391 - New database connection created successfully for thread 17816
2025-07-16 17:12:39.393 - Database connection closed for thread 17816
2025-07-16 17:12:39.394 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:12:39.395 - New database connection created successfully for thread 17816
2025-07-16 17:16:04.554 - Recreating expired connection for thread 16812
2025-07-16 17:16:04.651 - Using existing connection for thread 17816
2025-07-16 17:16:04.704 - Database connection closed for thread 17816
2025-07-16 17:16:04.708 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:16:04.709 - New database connection created successfully for thread 17816
2025-07-16 17:16:04.732 - Database connection closed for thread 17816
2025-07-16 17:16:04.735 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:16:04.739 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.357 - Using existing connection for thread 17816
2025-07-16 17:19:16.419 - Database connection closed for thread 17816
2025-07-16 17:19:16.434 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.435 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.435 - Database connection closed for thread 17816
2025-07-16 17:19:16.436 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.437 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.440 - Database connection closed for thread 17816
2025-07-16 17:19:16.442 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.443 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.447 - Database connection closed for thread 17816
2025-07-16 17:19:16.448 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.452 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.456 - Database connection closed for thread 17816
2025-07-16 17:19:16.458 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.459 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.479 - Database connection closed for thread 17816
2025-07-16 17:19:16.480 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.481 - New database connection created successfully for thread 17816
2025-07-16 17:19:16.485 - Database connection closed for thread 17816
2025-07-16 17:19:16.486 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:19:16.487 - New database connection created successfully for thread 17816
2025-07-16 17:21:18.911 - Recreating expired connection for thread 16812
2025-07-16 17:21:18.935 - Using existing connection for thread 17816
2025-07-16 17:21:19.033 - Database connection closed for thread 17816
2025-07-16 17:21:19.037 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:21:19.042 - New database connection created successfully for thread 17816
2025-07-16 17:21:19.049 - Database connection closed for thread 17816
2025-07-16 17:21:19.053 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:21:19.055 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.081 - Using existing connection for thread 17816
2025-07-16 17:24:07.125 - Database connection closed for thread 17816
2025-07-16 17:24:07.141 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.142 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.143 - Database connection closed for thread 17816
2025-07-16 17:24:07.144 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.145 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.148 - Database connection closed for thread 17816
2025-07-16 17:24:07.149 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.151 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.156 - Database connection closed for thread 17816
2025-07-16 17:24:07.157 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.158 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.163 - Database connection closed for thread 17816
2025-07-16 17:24:07.164 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.165 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.186 - Database connection closed for thread 17816
2025-07-16 17:24:07.187 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.188 - New database connection created successfully for thread 17816
2025-07-16 17:24:07.191 - Database connection closed for thread 17816
2025-07-16 17:24:07.193 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:24:07.194 - New database connection created successfully for thread 17816
2025-07-16 17:26:26.793 - Recreating expired connection for thread 16812
2025-07-16 17:26:26.827 - Using existing connection for thread 17816
2025-07-16 17:26:26.910 - Database connection closed for thread 17816
2025-07-16 17:26:26.911 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:26:26.912 - New database connection created successfully for thread 17816
2025-07-16 17:26:26.931 - Database connection closed for thread 17816
2025-07-16 17:26:26.933 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:26:26.935 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.292 - Using existing connection for thread 17816
2025-07-16 17:28:41.325 - Database connection closed for thread 17816
2025-07-16 17:28:41.340 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.341 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.342 - Database connection closed for thread 17816
2025-07-16 17:28:41.342 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.344 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.348 - Database connection closed for thread 17816
2025-07-16 17:28:41.349 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.351 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.358 - Database connection closed for thread 17816
2025-07-16 17:28:41.361 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.363 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.366 - Database connection closed for thread 17816
2025-07-16 17:28:41.367 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.368 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.390 - Database connection closed for thread 17816
2025-07-16 17:28:41.392 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.392 - New database connection created successfully for thread 17816
2025-07-16 17:28:41.396 - Database connection closed for thread 17816
2025-07-16 17:28:41.397 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:28:41.398 - New database connection created successfully for thread 17816
2025-07-16 17:31:12.271 - Using existing connection for thread 16812
2025-07-16 17:31:12.329 - Using existing connection for thread 17816
2025-07-16 17:31:12.416 - Database connection closed for thread 17816
2025-07-16 17:31:12.418 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:31:12.418 - New database connection created successfully for thread 17816
2025-07-16 17:31:12.430 - Database connection closed for thread 17816
2025-07-16 17:31:12.432 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:31:12.434 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.787 - Using existing connection for thread 17816
2025-07-16 17:33:34.825 - Database connection closed for thread 17816
2025-07-16 17:33:34.843 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.844 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.845 - Database connection closed for thread 17816
2025-07-16 17:33:34.845 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.846 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.850 - Database connection closed for thread 17816
2025-07-16 17:33:34.851 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.852 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.857 - Database connection closed for thread 17816
2025-07-16 17:33:34.860 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.862 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.865 - Database connection closed for thread 17816
2025-07-16 17:33:34.867 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.867 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.880 - Database connection closed for thread 17816
2025-07-16 17:33:34.882 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.883 - New database connection created successfully for thread 17816
2025-07-16 17:33:34.885 - Database connection closed for thread 17816
2025-07-16 17:33:34.886 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:33:34.887 - New database connection created successfully for thread 17816
2025-07-16 17:35:52.415 - Using existing connection for thread 16812
2025-07-16 17:35:52.467 - Using existing connection for thread 17816
2025-07-16 17:35:52.555 - Database connection closed for thread 17816
2025-07-16 17:35:52.559 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:35:52.560 - New database connection created successfully for thread 17816
2025-07-16 17:35:52.577 - Database connection closed for thread 17816
2025-07-16 17:35:52.580 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:35:52.583 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.588 - Using existing connection for thread 17816
2025-07-16 17:37:48.631 - Database connection closed for thread 17816
2025-07-16 17:37:48.648 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.648 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.650 - Database connection closed for thread 17816
2025-07-16 17:37:48.651 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.652 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.656 - Database connection closed for thread 17816
2025-07-16 17:37:48.657 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.658 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.663 - Database connection closed for thread 17816
2025-07-16 17:37:48.664 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.666 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.670 - Database connection closed for thread 17816
2025-07-16 17:37:48.671 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.673 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.695 - Database connection closed for thread 17816
2025-07-16 17:37:48.696 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.697 - New database connection created successfully for thread 17816
2025-07-16 17:37:48.700 - Database connection closed for thread 17816
2025-07-16 17:37:48.702 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:37:48.703 - New database connection created successfully for thread 17816
2025-07-16 17:40:21.132 - Using existing connection for thread 16812
2025-07-16 17:40:21.174 - Using existing connection for thread 17816
2025-07-16 17:40:21.220 - Database connection closed for thread 17816
2025-07-16 17:40:21.226 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:40:21.228 - New database connection created successfully for thread 17816
2025-07-16 17:40:21.236 - Database connection closed for thread 17816
2025-07-16 17:40:21.238 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:40:21.238 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.251 - Using existing connection for thread 17816
2025-07-16 17:44:02.302 - Database connection closed for thread 17816
2025-07-16 17:44:02.319 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.319 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.320 - Database connection closed for thread 17816
2025-07-16 17:44:02.321 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.322 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.326 - Database connection closed for thread 17816
2025-07-16 17:44:02.327 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.328 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.333 - Database connection closed for thread 17816
2025-07-16 17:44:02.334 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.338 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.341 - Database connection closed for thread 17816
2025-07-16 17:44:02.343 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.344 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.366 - Database connection closed for thread 17816
2025-07-16 17:44:02.368 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.369 - New database connection created successfully for thread 17816
2025-07-16 17:44:02.373 - Database connection closed for thread 17816
2025-07-16 17:44:02.374 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:44:02.375 - New database connection created successfully for thread 17816
2025-07-16 17:46:23.848 - Recreating expired connection for thread 16812
2025-07-16 17:46:23.929 - Using existing connection for thread 17816
2025-07-16 17:46:23.967 - Database connection closed for thread 17816
2025-07-16 17:46:23.972 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:46:23.975 - New database connection created successfully for thread 17816
2025-07-16 17:46:23.993 - Database connection closed for thread 17816
2025-07-16 17:46:24.001 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:46:24.003 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.264 - Using existing connection for thread 17816
2025-07-16 17:50:29.305 - Database connection closed for thread 17816
2025-07-16 17:50:29.322 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.323 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.324 - Database connection closed for thread 17816
2025-07-16 17:50:29.325 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.326 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.331 - Database connection closed for thread 17816
2025-07-16 17:50:29.332 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.335 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.341 - Database connection closed for thread 17816
2025-07-16 17:50:29.342 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.345 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.349 - Database connection closed for thread 17816
2025-07-16 17:50:29.350 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.351 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.365 - Database connection closed for thread 17816
2025-07-16 17:50:29.366 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.367 - New database connection created successfully for thread 17816
2025-07-16 17:50:29.370 - Database connection closed for thread 17816
2025-07-16 17:50:29.372 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:50:29.373 - New database connection created successfully for thread 17816
2025-07-16 17:55:41.564 - Recreating expired connection for thread 16812
2025-07-16 17:55:41.651 - Recreating expired connection for thread 17816
2025-07-16 17:55:41.732 - Database connection closed for thread 17816
2025-07-16 17:55:41.735 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:55:41.739 - New database connection created successfully for thread 17816
2025-07-16 17:55:41.755 - Database connection closed for thread 17816
2025-07-16 17:55:41.760 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:55:41.764 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.307 - Using existing connection for thread 17816
2025-07-16 17:59:06.348 - Database connection closed for thread 17816
2025-07-16 17:59:06.364 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.364 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.365 - Database connection closed for thread 17816
2025-07-16 17:59:06.366 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.366 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.370 - Database connection closed for thread 17816
2025-07-16 17:59:06.371 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.372 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.377 - Database connection closed for thread 17816
2025-07-16 17:59:06.379 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.380 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.385 - Database connection closed for thread 17816
2025-07-16 17:59:06.387 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.387 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.411 - Database connection closed for thread 17816
2025-07-16 17:59:06.413 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.414 - New database connection created successfully for thread 17816
2025-07-16 17:59:06.417 - Database connection closed for thread 17816
2025-07-16 17:59:06.418 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 17:59:06.420 - New database connection created successfully for thread 17816
2025-07-16 18:01:37.900 - Recreating expired connection for thread 16812
2025-07-16 18:01:37.918 - Using existing connection for thread 17816
2025-07-16 18:01:38.001 - Database connection closed for thread 17816
2025-07-16 18:01:38.009 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:01:38.012 - New database connection created successfully for thread 17816
2025-07-16 18:01:38.027 - Database connection closed for thread 17816
2025-07-16 18:01:38.031 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:01:38.034 - New database connection created successfully for thread 17816
2025-07-16 18:05:59.938 - Using existing connection for thread 17816
2025-07-16 18:05:59.989 - Database connection closed for thread 17816
2025-07-16 18:06:00.006 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.007 - New database connection created successfully for thread 17816
2025-07-16 18:06:00.008 - Database connection closed for thread 17816
2025-07-16 18:06:00.009 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.011 - New database connection created successfully for thread 17816
2025-07-16 18:06:00.014 - Database connection closed for thread 17816
2025-07-16 18:06:00.016 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.018 - New database connection created successfully for thread 17816
2025-07-16 18:06:00.023 - Database connection closed for thread 17816
2025-07-16 18:06:00.024 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.025 - New database connection created successfully for thread 17816
2025-07-16 18:06:00.031 - Database connection closed for thread 17816
2025-07-16 18:06:00.034 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.034 - New database connection created successfully for thread 17816
2025-07-16 18:06:00.050 - Database connection closed for thread 17816
2025-07-16 18:06:00.051 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.052 - New database connection created successfully for thread 17816
2025-07-16 18:06:00.060 - Database connection closed for thread 17816
2025-07-16 18:06:00.062 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:06:00.064 - New database connection created successfully for thread 17816
2025-07-16 18:08:56.441 - Recreating expired connection for thread 16812
2025-07-16 18:08:56.459 - Using existing connection for thread 17816
2025-07-16 18:08:56.587 - Database connection closed for thread 17816
2025-07-16 18:08:56.588 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:08:56.591 - New database connection created successfully for thread 17816
2025-07-16 18:08:56.605 - Database connection closed for thread 17816
2025-07-16 18:08:56.607 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:08:56.608 - New database connection created successfully for thread 17816
2025-07-16 18:12:35.890 - Using existing connection for thread 17816
2025-07-16 18:12:35.939 - Database connection closed for thread 17816
2025-07-16 18:12:35.955 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:35.956 - New database connection created successfully for thread 17816
2025-07-16 18:12:35.957 - Database connection closed for thread 17816
2025-07-16 18:12:35.958 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:35.958 - New database connection created successfully for thread 17816
2025-07-16 18:12:35.961 - Database connection closed for thread 17816
2025-07-16 18:12:35.962 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:35.963 - New database connection created successfully for thread 17816
2025-07-16 18:12:35.967 - Database connection closed for thread 17816
2025-07-16 18:12:35.968 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:35.970 - New database connection created successfully for thread 17816
2025-07-16 18:12:35.976 - Database connection closed for thread 17816
2025-07-16 18:12:35.977 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:35.979 - New database connection created successfully for thread 17816
2025-07-16 18:12:36.016 - Database connection closed for thread 17816
2025-07-16 18:12:36.017 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:36.018 - New database connection created successfully for thread 17816
2025-07-16 18:12:36.020 - Database connection closed for thread 17816
2025-07-16 18:12:36.022 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:12:36.023 - New database connection created successfully for thread 17816
2025-07-16 18:15:50.198 - Recreating expired connection for thread 16812
2025-07-16 18:15:50.265 - Using existing connection for thread 17816
2025-07-16 18:15:50.384 - Database connection closed for thread 17816
2025-07-16 18:15:50.386 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:15:50.387 - New database connection created successfully for thread 17816
2025-07-16 18:15:50.407 - Database connection closed for thread 17816
2025-07-16 18:15:50.411 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:15:50.416 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.804 - Using existing connection for thread 17816
2025-07-16 18:16:52.885 - Database connection closed for thread 17816
2025-07-16 18:16:52.897 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.898 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.899 - Database connection closed for thread 17816
2025-07-16 18:16:52.900 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.901 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.904 - Database connection closed for thread 17816
2025-07-16 18:16:52.906 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.907 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.911 - Database connection closed for thread 17816
2025-07-16 18:16:52.913 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.916 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.920 - Database connection closed for thread 17816
2025-07-16 18:16:52.921 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.922 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.938 - Database connection closed for thread 17816
2025-07-16 18:16:52.940 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.941 - New database connection created successfully for thread 17816
2025-07-16 18:16:52.950 - Database connection closed for thread 17816
2025-07-16 18:16:52.952 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:16:52.953 - New database connection created successfully for thread 17816
2025-07-16 18:19:55.805 - Using existing connection for thread 16812
2025-07-16 18:19:55.871 - Using existing connection for thread 17816
2025-07-16 18:19:55.978 - Database connection closed for thread 17816
2025-07-16 18:19:55.979 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:19:55.980 - New database connection created successfully for thread 17816
2025-07-16 18:19:55.996 - Database connection closed for thread 17816
2025-07-16 18:19:55.998 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:19:55.999 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.490 - Using existing connection for thread 17816
2025-07-16 18:21:52.537 - Database connection closed for thread 17816
2025-07-16 18:21:52.554 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.554 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.555 - Database connection closed for thread 17816
2025-07-16 18:21:52.556 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.557 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.560 - Database connection closed for thread 17816
2025-07-16 18:21:52.562 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.562 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.568 - Database connection closed for thread 17816
2025-07-16 18:21:52.569 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.572 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.576 - Database connection closed for thread 17816
2025-07-16 18:21:52.577 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.578 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.609 - Database connection closed for thread 17816
2025-07-16 18:21:52.610 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.611 - New database connection created successfully for thread 17816
2025-07-16 18:21:52.616 - Database connection closed for thread 17816
2025-07-16 18:21:52.617 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:21:52.618 - New database connection created successfully for thread 17816
2025-07-16 18:25:03.830 - Recreating expired connection for thread 16812
2025-07-16 18:25:03.907 - Using existing connection for thread 17816
2025-07-16 18:25:03.959 - Database connection closed for thread 17816
2025-07-16 18:25:03.965 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:25:03.969 - New database connection created successfully for thread 17816
2025-07-16 18:25:03.986 - Database connection closed for thread 17816
2025-07-16 18:25:03.993 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:25:03.998 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.391 - Using existing connection for thread 17816
2025-07-16 18:26:08.425 - Database connection closed for thread 17816
2025-07-16 18:26:08.443 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.445 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.446 - Database connection closed for thread 17816
2025-07-16 18:26:08.448 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.448 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.457 - Database connection closed for thread 17816
2025-07-16 18:26:08.459 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.459 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.465 - Database connection closed for thread 17816
2025-07-16 18:26:08.467 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.470 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.475 - Database connection closed for thread 17816
2025-07-16 18:26:08.477 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.479 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.502 - Database connection closed for thread 17816
2025-07-16 18:26:08.502 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.504 - New database connection created successfully for thread 17816
2025-07-16 18:26:08.507 - Database connection closed for thread 17816
2025-07-16 18:26:08.507 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:26:08.508 - New database connection created successfully for thread 17816
2025-07-16 18:28:50.627 - Using existing connection for thread 16812
2025-07-16 18:28:50.639 - Using existing connection for thread 17816
2025-07-16 18:28:50.743 - Database connection closed for thread 17816
2025-07-16 18:28:50.746 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:28:50.746 - New database connection created successfully for thread 17816
2025-07-16 18:28:50.761 - Database connection closed for thread 17816
2025-07-16 18:28:50.764 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:28:50.765 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.065 - Using existing connection for thread 17816
2025-07-16 18:30:57.091 - Database connection closed for thread 17816
2025-07-16 18:30:57.106 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.107 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.107 - Database connection closed for thread 17816
2025-07-16 18:30:57.108 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.108 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.113 - Database connection closed for thread 17816
2025-07-16 18:30:57.114 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.115 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.119 - Database connection closed for thread 17816
2025-07-16 18:30:57.121 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.122 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.128 - Database connection closed for thread 17816
2025-07-16 18:30:57.129 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.130 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.200 - Database connection closed for thread 17816
2025-07-16 18:30:57.201 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.204 - New database connection created successfully for thread 17816
2025-07-16 18:30:57.208 - Database connection closed for thread 17816
2025-07-16 18:30:57.209 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:30:57.211 - New database connection created successfully for thread 17816
2025-07-16 18:34:47.835 - Recreating expired connection for thread 16812
2025-07-16 18:34:47.917 - Using existing connection for thread 17816
2025-07-16 18:34:48.014 - Database connection closed for thread 17816
2025-07-16 18:34:48.020 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:34:48.025 - New database connection created successfully for thread 17816
2025-07-16 18:34:48.045 - Database connection closed for thread 17816
2025-07-16 18:34:48.051 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:34:48.055 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.332 - Using existing connection for thread 17816
2025-07-16 18:36:59.378 - Database connection closed for thread 17816
2025-07-16 18:36:59.397 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.398 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.400 - Database connection closed for thread 17816
2025-07-16 18:36:59.401 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.402 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.407 - Database connection closed for thread 17816
2025-07-16 18:36:59.408 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.410 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.418 - Database connection closed for thread 17816
2025-07-16 18:36:59.420 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.422 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.429 - Database connection closed for thread 17816
2025-07-16 18:36:59.431 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.434 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.445 - Database connection closed for thread 17816
2025-07-16 18:36:59.448 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.448 - New database connection created successfully for thread 17816
2025-07-16 18:36:59.452 - Database connection closed for thread 17816
2025-07-16 18:36:59.453 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:36:59.454 - New database connection created successfully for thread 17816
2025-07-16 18:40:16.085 - Recreating expired connection for thread 16812
2025-07-16 18:40:16.156 - Using existing connection for thread 17816
2025-07-16 18:40:16.258 - Database connection closed for thread 17816
2025-07-16 18:40:16.267 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:40:16.275 - New database connection created successfully for thread 17816
2025-07-16 18:40:16.288 - Database connection closed for thread 17816
2025-07-16 18:40:16.298 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:40:16.304 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.154 - Creating new thread-specific database connection to data\stats.db for thread 17292
2025-07-16 18:42:06.160 - New database connection created successfully for thread 17292
2025-07-16 18:42:06.164 - Using existing connection for thread 17816
2025-07-16 18:42:06.182 - Database connection closed for thread 17292
2025-07-16 18:42:06.188 - Creating new thread-specific database connection to data\stats.db for thread 17292
2025-07-16 18:42:06.193 - New database connection created successfully for thread 17292
2025-07-16 18:42:06.276 - Database connection closed for thread 17816
2025-07-16 18:42:06.289 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.296 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.301 - Database connection closed for thread 17816
2025-07-16 18:42:06.308 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.316 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.333 - Database connection closed for thread 17816
2025-07-16 18:42:06.339 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.345 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.377 - Database connection closed for thread 17816
2025-07-16 18:42:06.388 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.402 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.419 - Database connection closed for thread 17816
2025-07-16 18:42:06.423 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.428 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.439 - Database connection closed for thread 17816
2025-07-16 18:42:06.441 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.443 - New database connection created successfully for thread 17816
2025-07-16 18:42:06.480 - Database connection closed for thread 17816
2025-07-16 18:42:06.487 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:42:06.492 - New database connection created successfully for thread 17816
2025-07-16 18:43:33.926 - Using existing connection for thread 17816
2025-07-16 18:43:33.958 - Database connection closed for thread 17816
2025-07-16 18:43:33.972 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:33.973 - New database connection created successfully for thread 17816
2025-07-16 18:43:33.973 - Database connection closed for thread 17816
2025-07-16 18:43:33.974 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:33.975 - New database connection created successfully for thread 17816
2025-07-16 18:43:33.978 - Database connection closed for thread 17816
2025-07-16 18:43:33.980 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:33.980 - New database connection created successfully for thread 17816
2025-07-16 18:43:33.984 - Database connection closed for thread 17816
2025-07-16 18:43:33.986 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:33.988 - New database connection created successfully for thread 17816
2025-07-16 18:43:33.993 - Database connection closed for thread 17816
2025-07-16 18:43:33.994 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:33.995 - New database connection created successfully for thread 17816
2025-07-16 18:43:34.013 - Database connection closed for thread 17816
2025-07-16 18:43:34.017 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:34.018 - New database connection created successfully for thread 17816
2025-07-16 18:43:34.045 - Database connection closed for thread 17816
2025-07-16 18:43:34.046 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:43:34.047 - New database connection created successfully for thread 17816
2025-07-16 18:46:47.557 - Recreating expired connection for thread 16812
2025-07-16 18:46:47.623 - Using existing connection for thread 17816
2025-07-16 18:46:47.716 - Database connection closed for thread 17816
2025-07-16 18:46:47.724 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:46:47.729 - New database connection created successfully for thread 17816
2025-07-16 18:46:47.743 - Database connection closed for thread 17816
2025-07-16 18:46:47.754 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:46:47.760 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.024 - Using existing connection for thread 17816
2025-07-16 18:51:06.076 - Database connection closed for thread 17816
2025-07-16 18:51:06.090 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.091 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.092 - Database connection closed for thread 17816
2025-07-16 18:51:06.094 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.095 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.104 - Database connection closed for thread 17816
2025-07-16 18:51:06.106 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.107 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.112 - Database connection closed for thread 17816
2025-07-16 18:51:06.113 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.115 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.122 - Database connection closed for thread 17816
2025-07-16 18:51:06.124 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.125 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.148 - Database connection closed for thread 17816
2025-07-16 18:51:06.149 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.150 - New database connection created successfully for thread 17816
2025-07-16 18:51:06.152 - Database connection closed for thread 17816
2025-07-16 18:51:06.154 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:51:06.154 - New database connection created successfully for thread 17816
2025-07-16 18:54:50.245 - Recreating expired connection for thread 16812
2025-07-16 18:54:50.299 - Using existing connection for thread 17816
2025-07-16 18:54:50.405 - Database connection closed for thread 17816
2025-07-16 18:54:50.408 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:54:50.412 - New database connection created successfully for thread 17816
2025-07-16 18:54:50.434 - Database connection closed for thread 17816
2025-07-16 18:54:50.441 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:54:50.449 - New database connection created successfully for thread 17816
2025-07-16 18:57:35.973 - Using existing connection for thread 17816
2025-07-16 18:57:36.004 - Database connection closed for thread 17816
2025-07-16 18:57:36.025 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.026 - New database connection created successfully for thread 17816
2025-07-16 18:57:36.027 - Database connection closed for thread 17816
2025-07-16 18:57:36.031 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.032 - New database connection created successfully for thread 17816
2025-07-16 18:57:36.036 - Database connection closed for thread 17816
2025-07-16 18:57:36.038 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.039 - New database connection created successfully for thread 17816
2025-07-16 18:57:36.046 - Database connection closed for thread 17816
2025-07-16 18:57:36.047 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.049 - New database connection created successfully for thread 17816
2025-07-16 18:57:36.062 - Database connection closed for thread 17816
2025-07-16 18:57:36.063 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.064 - New database connection created successfully for thread 17816
2025-07-16 18:57:36.081 - Database connection closed for thread 17816
2025-07-16 18:57:36.081 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.082 - New database connection created successfully for thread 17816
2025-07-16 18:57:36.085 - Database connection closed for thread 17816
2025-07-16 18:57:36.087 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:57:36.088 - New database connection created successfully for thread 17816
2025-07-16 18:59:44.294 - Using existing connection for thread 16812
2025-07-16 18:59:44.309 - Using existing connection for thread 17816
2025-07-16 18:59:44.414 - Database connection closed for thread 17816
2025-07-16 18:59:44.416 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:59:44.417 - New database connection created successfully for thread 17816
2025-07-16 18:59:44.431 - Database connection closed for thread 17816
2025-07-16 18:59:44.433 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 18:59:44.434 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.102 - Using existing connection for thread 17816
2025-07-16 19:01:21.162 - Database connection closed for thread 17816
2025-07-16 19:01:21.177 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.178 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.178 - Database connection closed for thread 17816
2025-07-16 19:01:21.180 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.181 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.185 - Database connection closed for thread 17816
2025-07-16 19:01:21.186 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.187 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.191 - Database connection closed for thread 17816
2025-07-16 19:01:21.192 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.195 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.203 - Database connection closed for thread 17816
2025-07-16 19:01:21.205 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.207 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.236 - Database connection closed for thread 17816
2025-07-16 19:01:21.236 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.237 - New database connection created successfully for thread 17816
2025-07-16 19:01:21.241 - Database connection closed for thread 17816
2025-07-16 19:01:21.242 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:01:21.243 - New database connection created successfully for thread 17816
2025-07-16 19:05:12.403 - Recreating expired connection for thread 16812
2025-07-16 19:05:12.461 - Using existing connection for thread 17816
2025-07-16 19:05:12.564 - Database connection closed for thread 17816
2025-07-16 19:05:12.578 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:05:12.582 - New database connection created successfully for thread 17816
2025-07-16 19:05:12.598 - Database connection closed for thread 17816
2025-07-16 19:05:12.608 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:05:12.614 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.890 - Using existing connection for thread 17816
2025-07-16 19:06:47.920 - Database connection closed for thread 17816
2025-07-16 19:06:47.934 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.935 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.936 - Database connection closed for thread 17816
2025-07-16 19:06:47.936 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.937 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.942 - Database connection closed for thread 17816
2025-07-16 19:06:47.944 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.945 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.950 - Database connection closed for thread 17816
2025-07-16 19:06:47.951 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.953 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.959 - Database connection closed for thread 17816
2025-07-16 19:06:47.960 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.961 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.973 - Database connection closed for thread 17816
2025-07-16 19:06:47.975 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.976 - New database connection created successfully for thread 17816
2025-07-16 19:06:47.979 - Database connection closed for thread 17816
2025-07-16 19:06:47.980 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:06:47.982 - New database connection created successfully for thread 17816
2025-07-16 19:10:33.837 - Recreating expired connection for thread 16812
2025-07-16 19:10:33.930 - Using existing connection for thread 17816
2025-07-16 19:10:34.025 - Database connection closed for thread 17816
2025-07-16 19:10:34.031 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:10:34.033 - New database connection created successfully for thread 17816
2025-07-16 19:10:34.044 - Database connection closed for thread 17816
2025-07-16 19:10:34.050 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:10:34.054 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.657 - Using existing connection for thread 17816
2025-07-16 19:12:43.748 - Database connection closed for thread 17816
2025-07-16 19:12:43.763 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.764 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.764 - Database connection closed for thread 17816
2025-07-16 19:12:43.765 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.765 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.770 - Database connection closed for thread 17816
2025-07-16 19:12:43.772 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.773 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.777 - Database connection closed for thread 17816
2025-07-16 19:12:43.779 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.780 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.786 - Database connection closed for thread 17816
2025-07-16 19:12:43.787 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.788 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.804 - Database connection closed for thread 17816
2025-07-16 19:12:43.805 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.806 - New database connection created successfully for thread 17816
2025-07-16 19:12:43.810 - Database connection closed for thread 17816
2025-07-16 19:12:43.811 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:12:43.812 - New database connection created successfully for thread 17816
2025-07-16 19:15:20.241 - Using existing connection for thread 16812
2025-07-16 19:15:20.270 - Using existing connection for thread 17816
2025-07-16 19:15:20.381 - Database connection closed for thread 17816
2025-07-16 19:15:20.385 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:15:20.391 - New database connection created successfully for thread 17816
2025-07-16 19:15:20.398 - Database connection closed for thread 17816
2025-07-16 19:15:20.401 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:15:20.402 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.445 - Using existing connection for thread 17816
2025-07-16 19:18:20.494 - Database connection closed for thread 17816
2025-07-16 19:18:20.508 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.509 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.510 - Database connection closed for thread 17816
2025-07-16 19:18:20.510 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.511 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.515 - Database connection closed for thread 17816
2025-07-16 19:18:20.516 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.517 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.522 - Database connection closed for thread 17816
2025-07-16 19:18:20.524 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.525 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.530 - Database connection closed for thread 17816
2025-07-16 19:18:20.531 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.532 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.543 - Database connection closed for thread 17816
2025-07-16 19:18:20.546 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.547 - New database connection created successfully for thread 17816
2025-07-16 19:18:20.549 - Database connection closed for thread 17816
2025-07-16 19:18:20.551 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:18:20.552 - New database connection created successfully for thread 17816
2025-07-16 19:21:26.069 - Recreating expired connection for thread 16812
2025-07-16 19:21:26.170 - Using existing connection for thread 17816
2025-07-16 19:21:26.249 - Database connection closed for thread 17816
2025-07-16 19:21:26.251 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:21:26.252 - New database connection created successfully for thread 17816
2025-07-16 19:21:26.271 - Database connection closed for thread 17816
2025-07-16 19:21:26.278 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:21:26.284 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.216 - Using existing connection for thread 17816
2025-07-16 19:22:57.255 - Database connection closed for thread 17816
2025-07-16 19:22:57.270 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.271 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.272 - Database connection closed for thread 17816
2025-07-16 19:22:57.275 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.276 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.279 - Database connection closed for thread 17816
2025-07-16 19:22:57.281 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.281 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.285 - Database connection closed for thread 17816
2025-07-16 19:22:57.286 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.288 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.292 - Database connection closed for thread 17816
2025-07-16 19:22:57.293 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.295 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.320 - Database connection closed for thread 17816
2025-07-16 19:22:57.321 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.322 - New database connection created successfully for thread 17816
2025-07-16 19:22:57.330 - Database connection closed for thread 17816
2025-07-16 19:22:57.331 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:22:57.332 - New database connection created successfully for thread 17816
2025-07-16 19:25:47.542 - Using existing connection for thread 16812
2025-07-16 19:25:47.563 - Using existing connection for thread 17816
2025-07-16 19:25:47.662 - Database connection closed for thread 17816
2025-07-16 19:25:47.672 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:25:47.678 - New database connection created successfully for thread 17816
2025-07-16 19:25:47.695 - Database connection closed for thread 17816
2025-07-16 19:25:47.700 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:25:47.704 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.377 - Using existing connection for thread 17816
2025-07-16 19:27:59.413 - Database connection closed for thread 17816
2025-07-16 19:27:59.431 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.432 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.433 - Database connection closed for thread 17816
2025-07-16 19:27:59.433 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.437 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.440 - Database connection closed for thread 17816
2025-07-16 19:27:59.442 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.444 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.453 - Database connection closed for thread 17816
2025-07-16 19:27:59.456 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.457 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.465 - Database connection closed for thread 17816
2025-07-16 19:27:59.467 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.468 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.482 - Database connection closed for thread 17816
2025-07-16 19:27:59.483 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.484 - New database connection created successfully for thread 17816
2025-07-16 19:27:59.487 - Database connection closed for thread 17816
2025-07-16 19:27:59.488 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:27:59.489 - New database connection created successfully for thread 17816
2025-07-16 19:30:42.575 - Using existing connection for thread 16812
2025-07-16 19:30:42.605 - Using existing connection for thread 17816
2025-07-16 19:30:42.734 - Database connection closed for thread 17816
2025-07-16 19:30:42.735 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:30:42.737 - New database connection created successfully for thread 17816
2025-07-16 19:30:42.760 - Database connection closed for thread 17816
2025-07-16 19:30:42.768 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:30:42.775 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.595 - Using existing connection for thread 17816
2025-07-16 19:32:39.637 - Database connection closed for thread 17816
2025-07-16 19:32:39.656 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.657 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.658 - Database connection closed for thread 17816
2025-07-16 19:32:39.660 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.662 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.666 - Database connection closed for thread 17816
2025-07-16 19:32:39.667 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.669 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.674 - Database connection closed for thread 17816
2025-07-16 19:32:39.675 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.684 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.694 - Database connection closed for thread 17816
2025-07-16 19:32:39.695 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.697 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.713 - Database connection closed for thread 17816
2025-07-16 19:32:39.714 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.715 - New database connection created successfully for thread 17816
2025-07-16 19:32:39.719 - Database connection closed for thread 17816
2025-07-16 19:32:39.720 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:32:39.721 - New database connection created successfully for thread 17816
2025-07-16 19:34:22.606 - Using existing connection for thread 16812
2025-07-16 19:34:22.643 - Using existing connection for thread 17816
2025-07-16 19:34:22.793 - Database connection closed for thread 17816
2025-07-16 19:34:22.798 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:34:22.802 - New database connection created successfully for thread 17816
2025-07-16 19:34:22.817 - Database connection closed for thread 17816
2025-07-16 19:34:22.824 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:34:22.827 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.256 - Using existing connection for thread 17816
2025-07-16 19:37:18.296 - Database connection closed for thread 17816
2025-07-16 19:37:18.308 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.309 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.310 - Database connection closed for thread 17816
2025-07-16 19:37:18.310 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.311 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.315 - Database connection closed for thread 17816
2025-07-16 19:37:18.316 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.317 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.322 - Database connection closed for thread 17816
2025-07-16 19:37:18.324 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.326 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.334 - Database connection closed for thread 17816
2025-07-16 19:37:18.336 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.337 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.361 - Database connection closed for thread 17816
2025-07-16 19:37:18.362 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.363 - New database connection created successfully for thread 17816
2025-07-16 19:37:18.366 - Database connection closed for thread 17816
2025-07-16 19:37:18.367 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:37:18.368 - New database connection created successfully for thread 17816
2025-07-16 19:39:14.675 - Using existing connection for thread 16812
2025-07-16 19:39:14.770 - Using existing connection for thread 17816
2025-07-16 19:39:14.858 - Database connection closed for thread 17816
2025-07-16 19:39:14.867 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:39:14.874 - New database connection created successfully for thread 17816
2025-07-16 19:39:14.893 - Database connection closed for thread 17816
2025-07-16 19:39:14.902 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:39:14.909 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.686 - Using existing connection for thread 17816
2025-07-16 19:40:37.724 - Database connection closed for thread 17816
2025-07-16 19:40:37.739 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.740 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.741 - Database connection closed for thread 17816
2025-07-16 19:40:37.742 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.743 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.746 - Database connection closed for thread 17816
2025-07-16 19:40:37.747 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.749 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.754 - Database connection closed for thread 17816
2025-07-16 19:40:37.755 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.756 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.761 - Database connection closed for thread 17816
2025-07-16 19:40:37.762 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.763 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.829 - Database connection closed for thread 17816
2025-07-16 19:40:37.830 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.831 - New database connection created successfully for thread 17816
2025-07-16 19:40:37.836 - Database connection closed for thread 17816
2025-07-16 19:40:37.840 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:40:37.841 - New database connection created successfully for thread 17816
2025-07-16 19:43:14.395 - Using existing connection for thread 16812
2025-07-16 19:43:14.493 - Using existing connection for thread 17816
2025-07-16 19:43:14.585 - Database connection closed for thread 17816
2025-07-16 19:43:14.595 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:43:14.602 - New database connection created successfully for thread 17816
2025-07-16 19:43:14.623 - Database connection closed for thread 17816
2025-07-16 19:43:14.633 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:43:14.640 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.102 - Using existing connection for thread 17816
2025-07-16 19:46:22.145 - Database connection closed for thread 17816
2025-07-16 19:46:22.159 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.160 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.162 - Database connection closed for thread 17816
2025-07-16 19:46:22.162 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.163 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.165 - Database connection closed for thread 17816
2025-07-16 19:46:22.167 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.168 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.174 - Database connection closed for thread 17816
2025-07-16 19:46:22.175 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.176 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.199 - Database connection closed for thread 17816
2025-07-16 19:46:22.200 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.202 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.220 - Database connection closed for thread 17816
2025-07-16 19:46:22.221 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.222 - New database connection created successfully for thread 17816
2025-07-16 19:46:22.226 - Database connection closed for thread 17816
2025-07-16 19:46:22.227 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:46:22.229 - New database connection created successfully for thread 17816
2025-07-16 19:49:35.577 - Recreating expired connection for thread 16812
2025-07-16 19:49:35.669 - Using existing connection for thread 17816
2025-07-16 19:49:35.723 - Database connection closed for thread 17816
2025-07-16 19:49:35.725 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:49:35.726 - New database connection created successfully for thread 17816
2025-07-16 19:49:35.743 - Database connection closed for thread 17816
2025-07-16 19:49:35.745 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:49:35.746 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.135 - Using existing connection for thread 17816
2025-07-16 19:52:11.193 - Database connection closed for thread 17816
2025-07-16 19:52:11.207 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.208 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.209 - Database connection closed for thread 17816
2025-07-16 19:52:11.210 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.210 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.212 - Database connection closed for thread 17816
2025-07-16 19:52:11.214 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.215 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.220 - Database connection closed for thread 17816
2025-07-16 19:52:11.221 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.223 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.234 - Database connection closed for thread 17816
2025-07-16 19:52:11.235 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.236 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.258 - Database connection closed for thread 17816
2025-07-16 19:52:11.260 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.261 - New database connection created successfully for thread 17816
2025-07-16 19:52:11.265 - Database connection closed for thread 17816
2025-07-16 19:52:11.266 - Creating new thread-specific database connection to data\stats.db for thread 17816
2025-07-16 19:52:11.267 - New database connection created successfully for thread 17816
2025-07-16 19:52:46.421 - ensure_database_exists called from thread 12704
2025-07-16 19:52:46.422 - Creating new thread-specific database connection to data\stats.db for thread 12704
2025-07-16 19:52:46.423 - New database connection created successfully for thread 12704
2025-07-16 19:52:46.427 - Stats database initialized successfully
2025-07-16 19:52:46.428 - ensure_database_exists called from thread 12704
2025-07-16 19:52:46.429 - Using existing connection for thread 12704
2025-07-16 19:52:46.430 - Stats database initialized successfully
2025-07-16 19:52:46.431 - ensure_database_exists called from thread 12704
2025-07-16 19:52:46.432 - Using existing connection for thread 12704
2025-07-16 19:52:46.433 - Stats database initialized successfully
2025-07-16 19:54:49.685 - Using existing connection for thread 12704
2025-07-16 19:54:49.712 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:54:49.720 - New database connection created successfully for thread 11916
2025-07-16 19:54:49.882 - Database connection closed for thread 11916
2025-07-16 19:54:49.884 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:54:49.886 - New database connection created successfully for thread 11916
2025-07-16 19:54:49.900 - Database connection closed for thread 11916
2025-07-16 19:54:49.904 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:54:49.907 - New database connection created successfully for thread 11916
2025-07-16 19:54:49.961 - get_summary_stats called from thread 11916
2025-07-16 19:54:49.961 - Using existing connection for thread 11916
2025-07-16 19:54:49.963 - Total earnings from database: 1338.0
2025-07-16 19:54:49.967 - Daily earnings from database: 1332.0
2025-07-16 19:54:49.969 - Daily games from database: 31
2025-07-16 19:54:49.970 - Wallet balance from database: 0
2025-07-16 19:54:49.970 - Total games played from database: 32
2025-07-16 19:54:49.971 - Total winners from database: 32
2025-07-16 19:54:49.972 - Returning summary stats: {'total_earnings': 1338.0, 'daily_earnings': 1332.0, 'daily_games': 31, 'wallet_balance': 0}
2025-07-16 19:56:47.945 - Using existing connection for thread 11916
2025-07-16 19:56:48.032 - Database connection closed for thread 11916
2025-07-16 19:56:48.043 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.044 - New database connection created successfully for thread 11916
2025-07-16 19:56:48.044 - Database connection closed for thread 11916
2025-07-16 19:56:48.045 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.045 - New database connection created successfully for thread 11916
2025-07-16 19:56:48.049 - Database connection closed for thread 11916
2025-07-16 19:56:48.050 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.051 - New database connection created successfully for thread 11916
2025-07-16 19:56:48.059 - Database connection closed for thread 11916
2025-07-16 19:56:48.061 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.062 - New database connection created successfully for thread 11916
2025-07-16 19:56:48.072 - Database connection closed for thread 11916
2025-07-16 19:56:48.073 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.074 - New database connection created successfully for thread 11916
2025-07-16 19:56:48.114 - Database connection closed for thread 11916
2025-07-16 19:56:48.114 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.115 - New database connection created successfully for thread 11916
2025-07-16 19:56:48.117 - Database connection closed for thread 11916
2025-07-16 19:56:48.117 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:56:48.118 - New database connection created successfully for thread 11916
2025-07-16 19:58:56.444 - Using existing connection for thread 12704
2025-07-16 19:58:56.545 - Using existing connection for thread 11916
2025-07-16 19:58:56.596 - Database connection closed for thread 11916
2025-07-16 19:58:56.600 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:58:56.601 - New database connection created successfully for thread 11916
2025-07-16 19:58:56.612 - Database connection closed for thread 11916
2025-07-16 19:58:56.615 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 19:58:56.618 - New database connection created successfully for thread 11916
2025-07-16 20:00:19.906 - Using existing connection for thread 11916
2025-07-16 20:00:19.952 - Database connection closed for thread 11916
2025-07-16 20:00:19.967 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:19.976 - New database connection created successfully for thread 11916
2025-07-16 20:00:19.977 - Database connection closed for thread 11916
2025-07-16 20:00:19.979 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:19.980 - New database connection created successfully for thread 11916
2025-07-16 20:00:19.985 - Database connection closed for thread 11916
2025-07-16 20:00:19.988 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:19.989 - New database connection created successfully for thread 11916
2025-07-16 20:00:19.994 - Database connection closed for thread 11916
2025-07-16 20:00:19.995 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:19.997 - New database connection created successfully for thread 11916
2025-07-16 20:00:20.006 - Database connection closed for thread 11916
2025-07-16 20:00:20.012 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:20.014 - New database connection created successfully for thread 11916
2025-07-16 20:00:20.039 - Database connection closed for thread 11916
2025-07-16 20:00:20.040 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:20.042 - New database connection created successfully for thread 11916
2025-07-16 20:00:20.045 - Database connection closed for thread 11916
2025-07-16 20:00:20.046 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:00:20.047 - New database connection created successfully for thread 11916
2025-07-16 20:02:09.229 - Using existing connection for thread 12704
2025-07-16 20:02:09.314 - Using existing connection for thread 11916
2025-07-16 20:02:09.410 - Database connection closed for thread 11916
2025-07-16 20:02:09.413 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:02:09.416 - New database connection created successfully for thread 11916
2025-07-16 20:02:09.429 - Database connection closed for thread 11916
2025-07-16 20:02:09.432 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:02:09.433 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.256 - Using existing connection for thread 11916
2025-07-16 20:05:47.309 - Database connection closed for thread 11916
2025-07-16 20:05:47.326 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.326 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.327 - Database connection closed for thread 11916
2025-07-16 20:05:47.328 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.330 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.332 - Database connection closed for thread 11916
2025-07-16 20:05:47.333 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.334 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.339 - Database connection closed for thread 11916
2025-07-16 20:05:47.340 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.342 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.347 - Database connection closed for thread 11916
2025-07-16 20:05:47.349 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.350 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.362 - Database connection closed for thread 11916
2025-07-16 20:05:47.363 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.365 - New database connection created successfully for thread 11916
2025-07-16 20:05:47.371 - Database connection closed for thread 11916
2025-07-16 20:05:47.372 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:05:47.373 - New database connection created successfully for thread 11916
2025-07-16 20:08:03.103 - Recreating expired connection for thread 12704
2025-07-16 20:08:03.190 - Using existing connection for thread 11916
2025-07-16 20:08:03.279 - Database connection closed for thread 11916
2025-07-16 20:08:03.282 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:08:03.288 - New database connection created successfully for thread 11916
2025-07-16 20:08:03.294 - Database connection closed for thread 11916
2025-07-16 20:08:03.295 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:08:03.297 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.289 - Using existing connection for thread 11916
2025-07-16 20:09:48.366 - Database connection closed for thread 11916
2025-07-16 20:09:48.378 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.379 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.380 - Database connection closed for thread 11916
2025-07-16 20:09:48.381 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.382 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.385 - Database connection closed for thread 11916
2025-07-16 20:09:48.386 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.387 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.392 - Database connection closed for thread 11916
2025-07-16 20:09:48.395 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.396 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.403 - Database connection closed for thread 11916
2025-07-16 20:09:48.404 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.405 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.430 - Database connection closed for thread 11916
2025-07-16 20:09:48.431 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.437 - New database connection created successfully for thread 11916
2025-07-16 20:09:48.443 - Database connection closed for thread 11916
2025-07-16 20:09:48.447 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:09:48.448 - New database connection created successfully for thread 11916
2025-07-16 20:13:17.094 - Recreating expired connection for thread 12704
2025-07-16 20:13:17.114 - Using existing connection for thread 11916
2025-07-16 20:13:17.212 - Database connection closed for thread 11916
2025-07-16 20:13:17.214 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:13:17.215 - New database connection created successfully for thread 11916
2025-07-16 20:13:17.234 - Database connection closed for thread 11916
2025-07-16 20:13:17.239 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:13:17.241 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.830 - Using existing connection for thread 11916
2025-07-16 20:16:31.877 - Database connection closed for thread 11916
2025-07-16 20:16:31.891 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.892 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.892 - Database connection closed for thread 11916
2025-07-16 20:16:31.893 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.894 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.897 - Database connection closed for thread 11916
2025-07-16 20:16:31.898 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.900 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.916 - Database connection closed for thread 11916
2025-07-16 20:16:31.918 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.925 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.929 - Database connection closed for thread 11916
2025-07-16 20:16:31.930 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.931 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.942 - Database connection closed for thread 11916
2025-07-16 20:16:31.946 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.949 - New database connection created successfully for thread 11916
2025-07-16 20:16:31.957 - Database connection closed for thread 11916
2025-07-16 20:16:31.958 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:16:31.959 - New database connection created successfully for thread 11916
2025-07-16 20:20:06.669 - Recreating expired connection for thread 12704
2025-07-16 20:20:06.725 - Using existing connection for thread 11916
2025-07-16 20:20:06.814 - Database connection closed for thread 11916
2025-07-16 20:20:06.815 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:20:06.817 - New database connection created successfully for thread 11916
2025-07-16 20:20:06.828 - Database connection closed for thread 11916
2025-07-16 20:20:06.830 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:20:06.831 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.135 - Using existing connection for thread 11916
2025-07-16 20:23:56.174 - Database connection closed for thread 11916
2025-07-16 20:23:56.189 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.190 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.191 - Database connection closed for thread 11916
2025-07-16 20:23:56.191 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.192 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.196 - Database connection closed for thread 11916
2025-07-16 20:23:56.197 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.198 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.203 - Database connection closed for thread 11916
2025-07-16 20:23:56.206 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.207 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.213 - Database connection closed for thread 11916
2025-07-16 20:23:56.215 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.216 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.228 - Database connection closed for thread 11916
2025-07-16 20:23:56.229 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.232 - New database connection created successfully for thread 11916
2025-07-16 20:23:56.237 - Database connection closed for thread 11916
2025-07-16 20:23:56.238 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:23:56.239 - New database connection created successfully for thread 11916
2025-07-16 20:28:19.255 - Recreating expired connection for thread 12704
2025-07-16 20:28:19.336 - Using existing connection for thread 11916
2025-07-16 20:28:19.419 - Database connection closed for thread 11916
2025-07-16 20:28:19.428 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:28:19.435 - New database connection created successfully for thread 11916
2025-07-16 20:28:19.447 - Database connection closed for thread 11916
2025-07-16 20:28:19.449 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:28:19.450 - New database connection created successfully for thread 11916
2025-07-16 20:30:36.949 - Using existing connection for thread 11916
2025-07-16 20:30:36.980 - Database connection closed for thread 11916
2025-07-16 20:30:37.001 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.002 - New database connection created successfully for thread 11916
2025-07-16 20:30:37.003 - Database connection closed for thread 11916
2025-07-16 20:30:37.004 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.006 - New database connection created successfully for thread 11916
2025-07-16 20:30:37.016 - Database connection closed for thread 11916
2025-07-16 20:30:37.017 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.020 - New database connection created successfully for thread 11916
2025-07-16 20:30:37.027 - Database connection closed for thread 11916
2025-07-16 20:30:37.029 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.032 - New database connection created successfully for thread 11916
2025-07-16 20:30:37.037 - Database connection closed for thread 11916
2025-07-16 20:30:37.038 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.038 - New database connection created successfully for thread 11916
2025-07-16 20:30:37.060 - Database connection closed for thread 11916
2025-07-16 20:30:37.063 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.066 - New database connection created successfully for thread 11916
2025-07-16 20:30:37.069 - Database connection closed for thread 11916
2025-07-16 20:30:37.070 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:30:37.072 - New database connection created successfully for thread 11916
2025-07-16 20:32:47.982 - Using existing connection for thread 12704
2025-07-16 20:32:48.013 - Using existing connection for thread 11916
2025-07-16 20:32:48.101 - Database connection closed for thread 11916
2025-07-16 20:32:48.105 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:32:48.107 - New database connection created successfully for thread 11916
2025-07-16 20:32:48.129 - Database connection closed for thread 11916
2025-07-16 20:32:48.132 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:32:48.135 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.205 - Using existing connection for thread 11916
2025-07-16 20:35:10.237 - Database connection closed for thread 11916
2025-07-16 20:35:10.256 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.257 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.258 - Database connection closed for thread 11916
2025-07-16 20:35:10.259 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.260 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.265 - Database connection closed for thread 11916
2025-07-16 20:35:10.266 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.267 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.279 - Database connection closed for thread 11916
2025-07-16 20:35:10.283 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.286 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.291 - Database connection closed for thread 11916
2025-07-16 20:35:10.293 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.294 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.309 - Database connection closed for thread 11916
2025-07-16 20:35:10.310 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.310 - New database connection created successfully for thread 11916
2025-07-16 20:35:10.316 - Database connection closed for thread 11916
2025-07-16 20:35:10.317 - Creating new thread-specific database connection to data\stats.db for thread 11916
2025-07-16 20:35:10.318 - New database connection created successfully for thread 11916
2025-07-17 12:56:01.685 - ensure_database_exists called from thread 8500
2025-07-17 12:56:01.706 - Creating new thread-specific database connection to data\stats.db for thread 8500
2025-07-17 12:56:01.707 - New database connection created successfully for thread 8500
2025-07-17 12:56:01.777 - Stats database initialized successfully
2025-07-17 12:56:01.779 - ensure_database_exists called from thread 8500
2025-07-17 12:56:01.781 - Using existing connection for thread 8500
2025-07-17 12:56:01.782 - Stats database initialized successfully
2025-07-17 12:56:01.783 - ensure_database_exists called from thread 8500
2025-07-17 12:56:01.784 - Using existing connection for thread 8500
2025-07-17 12:56:01.785 - Stats database initialized successfully
2025-07-17 12:58:25.197 - Using existing connection for thread 8500
2025-07-17 12:58:25.254 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 12:58:25.261 - New database connection created successfully for thread 17092
2025-07-17 12:58:25.274 - Database connection closed for thread 17092
2025-07-17 12:58:25.277 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 12:58:25.278 - New database connection created successfully for thread 17092
2025-07-17 12:58:25.367 - get_summary_stats called from thread 17092
2025-07-17 12:58:25.370 - Using existing connection for thread 17092
2025-07-17 12:58:25.371 - Total earnings from database: 1678.0
2025-07-17 12:58:25.372 - Daily earnings from database: 0
2025-07-17 12:58:25.372 - Daily games from database: 0
2025-07-17 12:58:25.373 - Wallet balance from database: 0
2025-07-17 12:58:25.374 - Total games played from database: 40
2025-07-17 12:58:25.375 - Total winners from database: 40
2025-07-17 12:58:25.376 - Returning summary stats: {'total_earnings': 1678.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 13:06:24.207 - Recreating expired connection for thread 8500
2025-07-17 13:06:24.305 - Database connection closed for thread 17092
2025-07-17 13:06:24.306 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:06:24.307 - New database connection created successfully for thread 17092
2025-07-17 13:06:24.309 - Database connection closed for thread 17092
2025-07-17 13:06:24.310 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:06:24.311 - New database connection created successfully for thread 17092
2025-07-17 13:11:04.552 - Using existing connection for thread 8500
2025-07-17 13:11:04.581 - Database connection closed for thread 17092
2025-07-17 13:11:04.584 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:11:04.588 - New database connection created successfully for thread 17092
2025-07-17 13:11:04.601 - Database connection closed for thread 17092
2025-07-17 13:11:04.607 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:11:04.617 - New database connection created successfully for thread 17092
2025-07-17 13:18:42.493 - Recreating expired connection for thread 8500
2025-07-17 13:18:42.588 - Database connection closed for thread 17092
2025-07-17 13:18:42.593 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:18:42.595 - New database connection created successfully for thread 17092
2025-07-17 13:18:42.600 - Database connection closed for thread 17092
2025-07-17 13:18:42.601 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:18:42.604 - New database connection created successfully for thread 17092
2025-07-17 13:33:39.548 - Recreating expired connection for thread 8500
2025-07-17 13:33:39.594 - Database connection closed for thread 17092
2025-07-17 13:33:39.600 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:33:39.605 - New database connection created successfully for thread 17092
2025-07-17 13:33:39.613 - Database connection closed for thread 17092
2025-07-17 13:33:39.614 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:33:39.616 - New database connection created successfully for thread 17092
2025-07-17 13:39:55.764 - Recreating expired connection for thread 8500
2025-07-17 13:39:55.778 - Database connection closed for thread 17092
2025-07-17 13:39:55.780 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:39:55.782 - New database connection created successfully for thread 17092
2025-07-17 13:39:55.788 - Database connection closed for thread 17092
2025-07-17 13:39:55.790 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:39:55.794 - New database connection created successfully for thread 17092
2025-07-17 13:49:22.304 - Recreating expired connection for thread 8500
2025-07-17 13:49:22.354 - Database connection closed for thread 17092
2025-07-17 13:49:22.358 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:49:22.361 - New database connection created successfully for thread 17092
2025-07-17 13:49:22.369 - Database connection closed for thread 17092
2025-07-17 13:49:22.374 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:49:22.382 - New database connection created successfully for thread 17092
2025-07-17 13:57:25.251 - Recreating expired connection for thread 8500
2025-07-17 13:57:25.267 - Database connection closed for thread 17092
2025-07-17 13:57:25.270 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:57:25.273 - New database connection created successfully for thread 17092
2025-07-17 13:57:25.280 - Database connection closed for thread 17092
2025-07-17 13:57:25.285 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 13:57:25.290 - New database connection created successfully for thread 17092
2025-07-17 14:08:38.097 - Recreating expired connection for thread 8500
2025-07-17 14:08:38.113 - Database connection closed for thread 17092
2025-07-17 14:08:38.123 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:08:38.127 - New database connection created successfully for thread 17092
2025-07-17 14:08:38.132 - Database connection closed for thread 17092
2025-07-17 14:08:38.135 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:08:38.140 - New database connection created successfully for thread 17092
2025-07-17 14:16:45.442 - Recreating expired connection for thread 8500
2025-07-17 14:16:45.526 - Database connection closed for thread 17092
2025-07-17 14:16:45.530 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:16:45.533 - New database connection created successfully for thread 17092
2025-07-17 14:16:45.546 - Database connection closed for thread 17092
2025-07-17 14:16:45.554 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:16:45.564 - New database connection created successfully for thread 17092
2025-07-17 14:43:42.943 - Recreating expired connection for thread 8500
2025-07-17 14:43:42.948 - Database connection closed for thread 17092
2025-07-17 14:43:42.954 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:43:42.957 - New database connection created successfully for thread 17092
2025-07-17 14:43:42.967 - Database connection closed for thread 17092
2025-07-17 14:43:42.973 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:43:42.978 - New database connection created successfully for thread 17092
2025-07-17 14:50:14.402 - Recreating expired connection for thread 8500
2025-07-17 14:50:14.429 - Database connection closed for thread 17092
2025-07-17 14:50:14.432 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:50:14.435 - New database connection created successfully for thread 17092
2025-07-17 14:50:14.442 - Database connection closed for thread 17092
2025-07-17 14:50:14.448 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 14:50:14.451 - New database connection created successfully for thread 17092
2025-07-17 15:09:05.549 - Recreating expired connection for thread 8500
2025-07-17 15:09:05.645 - Database connection closed for thread 17092
2025-07-17 15:09:05.654 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:09:05.659 - New database connection created successfully for thread 17092
2025-07-17 15:09:05.670 - Database connection closed for thread 17092
2025-07-17 15:09:05.671 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:09:05.673 - New database connection created successfully for thread 17092
2025-07-17 15:16:38.211 - Recreating expired connection for thread 8500
2025-07-17 15:16:38.262 - Database connection closed for thread 17092
2025-07-17 15:16:38.266 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:16:38.271 - New database connection created successfully for thread 17092
2025-07-17 15:16:38.280 - Database connection closed for thread 17092
2025-07-17 15:16:38.287 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:16:38.292 - New database connection created successfully for thread 17092
2025-07-17 15:22:57.695 - Recreating expired connection for thread 8500
2025-07-17 15:22:57.780 - Database connection closed for thread 17092
2025-07-17 15:22:57.786 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:22:57.790 - New database connection created successfully for thread 17092
2025-07-17 15:22:57.801 - Database connection closed for thread 17092
2025-07-17 15:22:57.802 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:22:57.803 - New database connection created successfully for thread 17092
2025-07-17 15:30:04.256 - Recreating expired connection for thread 8500
2025-07-17 15:30:04.295 - Database connection closed for thread 17092
2025-07-17 15:30:04.299 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:30:04.303 - New database connection created successfully for thread 17092
2025-07-17 15:30:04.313 - Database connection closed for thread 17092
2025-07-17 15:30:04.317 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:30:04.322 - New database connection created successfully for thread 17092
2025-07-17 15:39:12.257 - Recreating expired connection for thread 8500
2025-07-17 15:39:12.317 - Database connection closed for thread 17092
2025-07-17 15:39:12.321 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:39:12.326 - New database connection created successfully for thread 17092
2025-07-17 15:39:12.338 - Database connection closed for thread 17092
2025-07-17 15:39:12.346 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:39:12.355 - New database connection created successfully for thread 17092
2025-07-17 15:43:51.592 - Using existing connection for thread 8500
2025-07-17 15:43:51.658 - Database connection closed for thread 17092
2025-07-17 15:43:51.662 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:43:51.666 - New database connection created successfully for thread 17092
2025-07-17 15:43:51.677 - Database connection closed for thread 17092
2025-07-17 15:43:51.685 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:43:51.692 - New database connection created successfully for thread 17092
2025-07-17 15:51:17.514 - Recreating expired connection for thread 8500
2025-07-17 15:51:17.553 - Database connection closed for thread 17092
2025-07-17 15:51:17.557 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:51:17.561 - New database connection created successfully for thread 17092
2025-07-17 15:51:17.572 - Database connection closed for thread 17092
2025-07-17 15:51:17.578 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:51:17.584 - New database connection created successfully for thread 17092
2025-07-17 15:57:28.572 - Recreating expired connection for thread 8500
2025-07-17 15:57:28.633 - Database connection closed for thread 17092
2025-07-17 15:57:28.638 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:57:28.642 - New database connection created successfully for thread 17092
2025-07-17 15:57:28.654 - Database connection closed for thread 17092
2025-07-17 15:57:28.655 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 15:57:28.656 - New database connection created successfully for thread 17092
2025-07-17 16:07:23.245 - Recreating expired connection for thread 8500
2025-07-17 16:07:23.302 - Database connection closed for thread 17092
2025-07-17 16:07:23.306 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:07:23.310 - New database connection created successfully for thread 17092
2025-07-17 16:07:23.320 - Database connection closed for thread 17092
2025-07-17 16:07:23.326 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:07:23.332 - New database connection created successfully for thread 17092
2025-07-17 16:15:51.720 - Recreating expired connection for thread 8500
2025-07-17 16:15:51.767 - Database connection closed for thread 17092
2025-07-17 16:15:51.771 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:15:51.775 - New database connection created successfully for thread 17092
2025-07-17 16:15:51.786 - Database connection closed for thread 17092
2025-07-17 16:15:51.793 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:15:51.798 - New database connection created successfully for thread 17092
2025-07-17 16:24:26.299 - Recreating expired connection for thread 8500
2025-07-17 16:24:26.346 - Database connection closed for thread 17092
2025-07-17 16:24:26.351 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:24:26.355 - New database connection created successfully for thread 17092
2025-07-17 16:24:26.367 - Database connection closed for thread 17092
2025-07-17 16:24:26.374 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:24:26.379 - New database connection created successfully for thread 17092
2025-07-17 16:33:03.952 - Recreating expired connection for thread 8500
2025-07-17 16:33:04.033 - Recreating expired connection for thread 17092
2025-07-17 16:33:04.146 - Database connection closed for thread 17092
2025-07-17 16:33:04.151 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:33:04.156 - New database connection created successfully for thread 17092
2025-07-17 16:33:04.166 - Database connection closed for thread 17092
2025-07-17 16:33:04.172 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:33:04.178 - New database connection created successfully for thread 17092
2025-07-17 16:34:58.617 - Using existing connection for thread 17092
2025-07-17 16:34:58.768 - Database connection closed for thread 17092
2025-07-17 16:34:58.805 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:58.806 - New database connection created successfully for thread 17092
2025-07-17 16:34:58.807 - Database connection closed for thread 17092
2025-07-17 16:34:58.808 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:58.814 - New database connection created successfully for thread 17092
2025-07-17 16:34:58.838 - Database connection closed for thread 17092
2025-07-17 16:34:58.844 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:58.850 - New database connection created successfully for thread 17092
2025-07-17 16:34:58.902 - Database connection closed for thread 17092
2025-07-17 16:34:58.923 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:58.928 - New database connection created successfully for thread 17092
2025-07-17 16:34:58.944 - Database connection closed for thread 17092
2025-07-17 16:34:58.950 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:58.956 - New database connection created successfully for thread 17092
2025-07-17 16:34:59.052 - Database connection closed for thread 17092
2025-07-17 16:34:59.071 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:59.087 - New database connection created successfully for thread 17092
2025-07-17 16:34:59.104 - Database connection closed for thread 17092
2025-07-17 16:34:59.113 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:34:59.120 - New database connection created successfully for thread 17092
2025-07-17 16:36:59.376 - Using existing connection for thread 8500
2025-07-17 16:36:59.467 - Using existing connection for thread 17092
2025-07-17 16:36:59.519 - Database connection closed for thread 17092
2025-07-17 16:36:59.520 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:36:59.522 - New database connection created successfully for thread 17092
2025-07-17 16:36:59.540 - Database connection closed for thread 17092
2025-07-17 16:36:59.542 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:36:59.544 - New database connection created successfully for thread 17092
2025-07-17 16:39:00.900 - Using existing connection for thread 17092
2025-07-17 16:39:00.946 - Database connection closed for thread 17092
2025-07-17 16:39:00.965 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:00.966 - New database connection created successfully for thread 17092
2025-07-17 16:39:00.967 - Database connection closed for thread 17092
2025-07-17 16:39:00.968 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:00.969 - New database connection created successfully for thread 17092
2025-07-17 16:39:00.972 - Database connection closed for thread 17092
2025-07-17 16:39:00.973 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:00.974 - New database connection created successfully for thread 17092
2025-07-17 16:39:00.981 - Database connection closed for thread 17092
2025-07-17 16:39:00.983 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:00.984 - New database connection created successfully for thread 17092
2025-07-17 16:39:00.992 - Database connection closed for thread 17092
2025-07-17 16:39:00.993 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:00.994 - New database connection created successfully for thread 17092
2025-07-17 16:39:01.018 - Database connection closed for thread 17092
2025-07-17 16:39:01.020 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:01.024 - New database connection created successfully for thread 17092
2025-07-17 16:39:01.027 - Database connection closed for thread 17092
2025-07-17 16:39:01.028 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:39:01.030 - New database connection created successfully for thread 17092
2025-07-17 16:40:44.704 - Using existing connection for thread 8500
2025-07-17 16:40:44.791 - Using existing connection for thread 17092
2025-07-17 16:40:44.890 - Database connection closed for thread 17092
2025-07-17 16:40:44.893 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:40:44.895 - New database connection created successfully for thread 17092
2025-07-17 16:40:44.922 - Database connection closed for thread 17092
2025-07-17 16:40:44.927 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:40:44.933 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.612 - Using existing connection for thread 17092
2025-07-17 16:43:14.685 - Database connection closed for thread 17092
2025-07-17 16:43:14.697 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.699 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.700 - Database connection closed for thread 17092
2025-07-17 16:43:14.701 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.702 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.705 - Database connection closed for thread 17092
2025-07-17 16:43:14.707 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.708 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.712 - Database connection closed for thread 17092
2025-07-17 16:43:14.714 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.715 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.721 - Database connection closed for thread 17092
2025-07-17 16:43:14.723 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.723 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.733 - Database connection closed for thread 17092
2025-07-17 16:43:14.735 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.737 - New database connection created successfully for thread 17092
2025-07-17 16:43:14.742 - Database connection closed for thread 17092
2025-07-17 16:43:14.743 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:43:14.744 - New database connection created successfully for thread 17092
2025-07-17 16:45:41.085 - Using existing connection for thread 8500
2025-07-17 16:45:41.112 - Using existing connection for thread 17092
2025-07-17 16:45:41.201 - Database connection closed for thread 17092
2025-07-17 16:45:41.202 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:45:41.203 - New database connection created successfully for thread 17092
2025-07-17 16:45:41.218 - Database connection closed for thread 17092
2025-07-17 16:45:41.221 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:45:41.222 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.279 - Using existing connection for thread 17092
2025-07-17 16:47:44.358 - Database connection closed for thread 17092
2025-07-17 16:47:44.373 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.374 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.375 - Database connection closed for thread 17092
2025-07-17 16:47:44.375 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.376 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.379 - Database connection closed for thread 17092
2025-07-17 16:47:44.380 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.381 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.386 - Database connection closed for thread 17092
2025-07-17 16:47:44.388 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.390 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.397 - Database connection closed for thread 17092
2025-07-17 16:47:44.399 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.400 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.421 - Database connection closed for thread 17092
2025-07-17 16:47:44.421 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.422 - New database connection created successfully for thread 17092
2025-07-17 16:47:44.425 - Database connection closed for thread 17092
2025-07-17 16:47:44.426 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:47:44.427 - New database connection created successfully for thread 17092
2025-07-17 16:49:15.289 - Using existing connection for thread 8500
2025-07-17 16:49:15.324 - Using existing connection for thread 17092
2025-07-17 16:49:15.400 - Database connection closed for thread 17092
2025-07-17 16:49:15.406 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:49:15.414 - New database connection created successfully for thread 17092
2025-07-17 16:49:15.432 - Database connection closed for thread 17092
2025-07-17 16:49:15.440 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:49:15.441 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.044 - Using existing connection for thread 17092
2025-07-17 16:51:45.111 - Database connection closed for thread 17092
2025-07-17 16:51:45.126 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.127 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.128 - Database connection closed for thread 17092
2025-07-17 16:51:45.128 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.130 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.133 - Database connection closed for thread 17092
2025-07-17 16:51:45.134 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.136 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.140 - Database connection closed for thread 17092
2025-07-17 16:51:45.142 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.144 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.148 - Database connection closed for thread 17092
2025-07-17 16:51:45.152 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.153 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.166 - Database connection closed for thread 17092
2025-07-17 16:51:45.171 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.172 - New database connection created successfully for thread 17092
2025-07-17 16:51:45.178 - Database connection closed for thread 17092
2025-07-17 16:51:45.181 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:51:45.182 - New database connection created successfully for thread 17092
2025-07-17 16:54:02.309 - Using existing connection for thread 8500
2025-07-17 16:54:02.395 - Using existing connection for thread 17092
2025-07-17 16:54:02.479 - Database connection closed for thread 17092
2025-07-17 16:54:02.480 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:54:02.482 - New database connection created successfully for thread 17092
2025-07-17 16:54:02.503 - Database connection closed for thread 17092
2025-07-17 16:54:02.510 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:54:02.514 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.735 - Using existing connection for thread 17092
2025-07-17 16:57:30.803 - Database connection closed for thread 17092
2025-07-17 16:57:30.816 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.817 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.818 - Database connection closed for thread 17092
2025-07-17 16:57:30.818 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.819 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.823 - Database connection closed for thread 17092
2025-07-17 16:57:30.824 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.825 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.832 - Database connection closed for thread 17092
2025-07-17 16:57:30.834 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.835 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.845 - Database connection closed for thread 17092
2025-07-17 16:57:30.846 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.848 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.869 - Database connection closed for thread 17092
2025-07-17 16:57:30.870 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.871 - New database connection created successfully for thread 17092
2025-07-17 16:57:30.878 - Database connection closed for thread 17092
2025-07-17 16:57:30.879 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 16:57:30.880 - New database connection created successfully for thread 17092
2025-07-17 17:00:15.331 - Recreating expired connection for thread 8500
2025-07-17 17:00:15.381 - Using existing connection for thread 17092
2025-07-17 17:00:15.464 - Database connection closed for thread 17092
2025-07-17 17:00:15.470 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:00:15.471 - New database connection created successfully for thread 17092
2025-07-17 17:00:15.479 - Database connection closed for thread 17092
2025-07-17 17:00:15.480 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:00:15.481 - New database connection created successfully for thread 17092
2025-07-17 17:02:44.909 - Using existing connection for thread 17092
2025-07-17 17:02:44.957 - Database connection closed for thread 17092
2025-07-17 17:02:44.981 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:44.982 - New database connection created successfully for thread 17092
2025-07-17 17:02:44.983 - Database connection closed for thread 17092
2025-07-17 17:02:44.984 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:44.985 - New database connection created successfully for thread 17092
2025-07-17 17:02:44.994 - Database connection closed for thread 17092
2025-07-17 17:02:44.996 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:44.998 - New database connection created successfully for thread 17092
2025-07-17 17:02:45.006 - Database connection closed for thread 17092
2025-07-17 17:02:45.008 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:45.009 - New database connection created successfully for thread 17092
2025-07-17 17:02:45.021 - Database connection closed for thread 17092
2025-07-17 17:02:45.023 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:45.024 - New database connection created successfully for thread 17092
2025-07-17 17:02:45.041 - Database connection closed for thread 17092
2025-07-17 17:02:45.042 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:45.043 - New database connection created successfully for thread 17092
2025-07-17 17:02:45.047 - Database connection closed for thread 17092
2025-07-17 17:02:45.048 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:02:45.049 - New database connection created successfully for thread 17092
2025-07-17 17:05:16.895 - Recreating expired connection for thread 8500
2025-07-17 17:05:16.972 - Using existing connection for thread 17092
2025-07-17 17:05:17.056 - Database connection closed for thread 17092
2025-07-17 17:05:17.064 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:05:17.069 - New database connection created successfully for thread 17092
2025-07-17 17:05:17.083 - Database connection closed for thread 17092
2025-07-17 17:05:17.093 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:05:17.098 - New database connection created successfully for thread 17092
2025-07-17 17:08:13.982 - Using existing connection for thread 17092
2025-07-17 17:08:14.055 - Database connection closed for thread 17092
2025-07-17 17:08:14.070 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.071 - New database connection created successfully for thread 17092
2025-07-17 17:08:14.071 - Database connection closed for thread 17092
2025-07-17 17:08:14.072 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.073 - New database connection created successfully for thread 17092
2025-07-17 17:08:14.075 - Database connection closed for thread 17092
2025-07-17 17:08:14.076 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.078 - New database connection created successfully for thread 17092
2025-07-17 17:08:14.083 - Database connection closed for thread 17092
2025-07-17 17:08:14.084 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.085 - New database connection created successfully for thread 17092
2025-07-17 17:08:14.091 - Database connection closed for thread 17092
2025-07-17 17:08:14.092 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.093 - New database connection created successfully for thread 17092
2025-07-17 17:08:14.104 - Database connection closed for thread 17092
2025-07-17 17:08:14.108 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.108 - New database connection created successfully for thread 17092
2025-07-17 17:08:14.112 - Database connection closed for thread 17092
2025-07-17 17:08:14.113 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:08:14.114 - New database connection created successfully for thread 17092
2025-07-17 17:10:23.777 - Recreating expired connection for thread 8500
2025-07-17 17:10:23.867 - Using existing connection for thread 17092
2025-07-17 17:10:23.939 - Database connection closed for thread 17092
2025-07-17 17:10:23.948 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:10:23.951 - New database connection created successfully for thread 17092
2025-07-17 17:10:23.969 - Database connection closed for thread 17092
2025-07-17 17:10:23.979 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:10:23.983 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.255 - Using existing connection for thread 17092
2025-07-17 17:12:23.297 - Database connection closed for thread 17092
2025-07-17 17:12:23.318 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.319 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.320 - Database connection closed for thread 17092
2025-07-17 17:12:23.321 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.321 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.325 - Database connection closed for thread 17092
2025-07-17 17:12:23.327 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.327 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.332 - Database connection closed for thread 17092
2025-07-17 17:12:23.336 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.339 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.343 - Database connection closed for thread 17092
2025-07-17 17:12:23.346 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.347 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.369 - Database connection closed for thread 17092
2025-07-17 17:12:23.371 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.372 - New database connection created successfully for thread 17092
2025-07-17 17:12:23.375 - Database connection closed for thread 17092
2025-07-17 17:12:23.377 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:12:23.378 - New database connection created successfully for thread 17092
2025-07-17 17:14:28.050 - Using existing connection for thread 8500
2025-07-17 17:14:28.094 - Using existing connection for thread 17092
2025-07-17 17:14:28.173 - Database connection closed for thread 17092
2025-07-17 17:14:28.183 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:14:28.190 - New database connection created successfully for thread 17092
2025-07-17 17:14:28.209 - Database connection closed for thread 17092
2025-07-17 17:14:28.216 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:14:28.221 - New database connection created successfully for thread 17092
2025-07-17 17:15:36.929 - Using existing connection for thread 17092
2025-07-17 17:15:37.052 - Database connection closed for thread 17092
2025-07-17 17:15:37.064 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.066 - New database connection created successfully for thread 17092
2025-07-17 17:15:37.067 - Database connection closed for thread 17092
2025-07-17 17:15:37.067 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.069 - New database connection created successfully for thread 17092
2025-07-17 17:15:37.072 - Database connection closed for thread 17092
2025-07-17 17:15:37.074 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.074 - New database connection created successfully for thread 17092
2025-07-17 17:15:37.082 - Database connection closed for thread 17092
2025-07-17 17:15:37.084 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.085 - New database connection created successfully for thread 17092
2025-07-17 17:15:37.092 - Database connection closed for thread 17092
2025-07-17 17:15:37.095 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.098 - New database connection created successfully for thread 17092
2025-07-17 17:15:37.111 - Database connection closed for thread 17092
2025-07-17 17:15:37.112 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.113 - New database connection created successfully for thread 17092
2025-07-17 17:15:37.117 - Database connection closed for thread 17092
2025-07-17 17:15:37.118 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:15:37.119 - New database connection created successfully for thread 17092
2025-07-17 17:16:59.222 - Using existing connection for thread 8500
2025-07-17 17:16:59.306 - Using existing connection for thread 17092
2025-07-17 17:16:59.419 - Database connection closed for thread 17092
2025-07-17 17:16:59.427 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:16:59.433 - New database connection created successfully for thread 17092
2025-07-17 17:16:59.450 - Database connection closed for thread 17092
2025-07-17 17:16:59.457 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:16:59.463 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.562 - Using existing connection for thread 17092
2025-07-17 17:19:23.665 - Database connection closed for thread 17092
2025-07-17 17:19:23.678 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.679 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.680 - Database connection closed for thread 17092
2025-07-17 17:19:23.680 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.681 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.684 - Database connection closed for thread 17092
2025-07-17 17:19:23.685 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.686 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.691 - Database connection closed for thread 17092
2025-07-17 17:19:23.693 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.693 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.700 - Database connection closed for thread 17092
2025-07-17 17:19:23.701 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.702 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.726 - Database connection closed for thread 17092
2025-07-17 17:19:23.726 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.727 - New database connection created successfully for thread 17092
2025-07-17 17:19:23.731 - Database connection closed for thread 17092
2025-07-17 17:19:23.732 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:19:23.733 - New database connection created successfully for thread 17092
2025-07-17 17:21:59.643 - Recreating expired connection for thread 8500
2025-07-17 17:21:59.678 - Using existing connection for thread 17092
2025-07-17 17:21:59.792 - Database connection closed for thread 17092
2025-07-17 17:21:59.795 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:21:59.796 - New database connection created successfully for thread 17092
2025-07-17 17:21:59.809 - Database connection closed for thread 17092
2025-07-17 17:21:59.810 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:21:59.811 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.559 - Using existing connection for thread 17092
2025-07-17 17:23:12.606 - Database connection closed for thread 17092
2025-07-17 17:23:12.619 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.620 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.620 - Database connection closed for thread 17092
2025-07-17 17:23:12.621 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.622 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.626 - Database connection closed for thread 17092
2025-07-17 17:23:12.628 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.629 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.633 - Database connection closed for thread 17092
2025-07-17 17:23:12.635 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.637 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.643 - Database connection closed for thread 17092
2025-07-17 17:23:12.643 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.645 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.675 - Database connection closed for thread 17092
2025-07-17 17:23:12.676 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.677 - New database connection created successfully for thread 17092
2025-07-17 17:23:12.681 - Database connection closed for thread 17092
2025-07-17 17:23:12.683 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:23:12.684 - New database connection created successfully for thread 17092
2025-07-17 17:26:01.084 - Using existing connection for thread 8500
2025-07-17 17:26:01.112 - Using existing connection for thread 17092
2025-07-17 17:26:01.217 - Database connection closed for thread 17092
2025-07-17 17:26:01.219 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:26:01.220 - New database connection created successfully for thread 17092
2025-07-17 17:26:01.234 - Database connection closed for thread 17092
2025-07-17 17:26:01.235 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:26:01.236 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.744 - Using existing connection for thread 17092
2025-07-17 17:28:07.799 - Database connection closed for thread 17092
2025-07-17 17:28:07.811 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.812 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.812 - Database connection closed for thread 17092
2025-07-17 17:28:07.814 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.814 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.817 - Database connection closed for thread 17092
2025-07-17 17:28:07.819 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.820 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.824 - Database connection closed for thread 17092
2025-07-17 17:28:07.826 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.827 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.832 - Database connection closed for thread 17092
2025-07-17 17:28:07.834 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.835 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.864 - Database connection closed for thread 17092
2025-07-17 17:28:07.867 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.868 - New database connection created successfully for thread 17092
2025-07-17 17:28:07.871 - Database connection closed for thread 17092
2025-07-17 17:28:07.872 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:28:07.874 - New database connection created successfully for thread 17092
2025-07-17 17:29:58.450 - Using existing connection for thread 8500
2025-07-17 17:29:58.535 - Using existing connection for thread 17092
2025-07-17 17:29:58.781 - Database connection closed for thread 17092
2025-07-17 17:29:58.789 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:29:58.794 - New database connection created successfully for thread 17092
2025-07-17 17:29:58.812 - Database connection closed for thread 17092
2025-07-17 17:29:58.822 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:29:58.827 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.537 - Using existing connection for thread 17092
2025-07-17 17:32:51.619 - Database connection closed for thread 17092
2025-07-17 17:32:51.632 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.632 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.633 - Database connection closed for thread 17092
2025-07-17 17:32:51.634 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.635 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.638 - Database connection closed for thread 17092
2025-07-17 17:32:51.639 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.640 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.644 - Database connection closed for thread 17092
2025-07-17 17:32:51.644 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.646 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.650 - Database connection closed for thread 17092
2025-07-17 17:32:51.653 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.654 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.664 - Database connection closed for thread 17092
2025-07-17 17:32:51.666 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.669 - New database connection created successfully for thread 17092
2025-07-17 17:32:51.673 - Database connection closed for thread 17092
2025-07-17 17:32:51.674 - Creating new thread-specific database connection to data\stats.db for thread 17092
2025-07-17 17:32:51.675 - New database connection created successfully for thread 17092
2025-07-17 17:32:58.487 - ensure_database_exists called from thread 12812
2025-07-17 17:32:58.488 - Creating new thread-specific database connection to data\stats.db for thread 12812
2025-07-17 17:32:58.489 - New database connection created successfully for thread 12812
2025-07-17 17:32:58.491 - Stats database initialized successfully
2025-07-17 17:32:58.493 - ensure_database_exists called from thread 12812
2025-07-17 17:32:58.494 - Using existing connection for thread 12812
2025-07-17 17:32:58.495 - Stats database initialized successfully
2025-07-17 17:32:58.496 - ensure_database_exists called from thread 12812
2025-07-17 17:32:58.497 - Using existing connection for thread 12812
2025-07-17 17:32:58.498 - Stats database initialized successfully
2025-07-17 17:34:31.740 - Using existing connection for thread 12812
2025-07-17 17:34:31.753 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:34:31.762 - New database connection created successfully for thread 13396
2025-07-17 17:34:31.841 - Database connection closed for thread 13396
2025-07-17 17:34:31.843 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:34:31.849 - New database connection created successfully for thread 13396
2025-07-17 17:34:31.864 - Database connection closed for thread 13396
2025-07-17 17:34:31.874 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:34:31.877 - New database connection created successfully for thread 13396
2025-07-17 17:34:31.920 - get_summary_stats called from thread 13396
2025-07-17 17:34:31.923 - Using existing connection for thread 13396
2025-07-17 17:34:31.925 - Total earnings from database: 2090.0
2025-07-17 17:34:31.927 - Daily earnings from database: 412.0
2025-07-17 17:34:31.930 - Daily games from database: 14
2025-07-17 17:34:31.932 - Wallet balance from database: 0
2025-07-17 17:34:31.933 - Total games played from database: 54
2025-07-17 17:34:31.934 - Total winners from database: 54
2025-07-17 17:34:31.937 - Returning summary stats: {'total_earnings': 2090.0, 'daily_earnings': 412.0, 'daily_games': 14, 'wallet_balance': 0}
2025-07-17 17:35:38.031 - Using existing connection for thread 13396
2025-07-17 17:35:38.126 - Database connection closed for thread 13396
2025-07-17 17:35:38.141 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.142 - New database connection created successfully for thread 13396
2025-07-17 17:35:38.143 - Database connection closed for thread 13396
2025-07-17 17:35:38.144 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.145 - New database connection created successfully for thread 13396
2025-07-17 17:35:38.147 - Database connection closed for thread 13396
2025-07-17 17:35:38.148 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.150 - New database connection created successfully for thread 13396
2025-07-17 17:35:38.156 - Database connection closed for thread 13396
2025-07-17 17:35:38.157 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.158 - New database connection created successfully for thread 13396
2025-07-17 17:35:38.165 - Database connection closed for thread 13396
2025-07-17 17:35:38.166 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.167 - New database connection created successfully for thread 13396
2025-07-17 17:35:38.178 - Database connection closed for thread 13396
2025-07-17 17:35:38.187 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.188 - New database connection created successfully for thread 13396
2025-07-17 17:35:38.192 - Database connection closed for thread 13396
2025-07-17 17:35:38.193 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:35:38.194 - New database connection created successfully for thread 13396
2025-07-17 17:37:45.238 - Using existing connection for thread 12812
2025-07-17 17:37:45.259 - Using existing connection for thread 13396
2025-07-17 17:37:45.398 - Database connection closed for thread 13396
2025-07-17 17:37:45.400 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:37:45.405 - New database connection created successfully for thread 13396
2025-07-17 17:37:45.415 - Database connection closed for thread 13396
2025-07-17 17:37:45.419 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:37:45.423 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.207 - Using existing connection for thread 13396
2025-07-17 17:39:07.276 - Database connection closed for thread 13396
2025-07-17 17:39:07.299 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.300 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.302 - Database connection closed for thread 13396
2025-07-17 17:39:07.303 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.304 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.309 - Database connection closed for thread 13396
2025-07-17 17:39:07.310 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.312 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.328 - Database connection closed for thread 13396
2025-07-17 17:39:07.330 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.332 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.337 - Database connection closed for thread 13396
2025-07-17 17:39:07.338 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.340 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.356 - Database connection closed for thread 13396
2025-07-17 17:39:07.360 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.360 - New database connection created successfully for thread 13396
2025-07-17 17:39:07.363 - Database connection closed for thread 13396
2025-07-17 17:39:07.365 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:39:07.366 - New database connection created successfully for thread 13396
2025-07-17 17:41:12.061 - Using existing connection for thread 12812
2025-07-17 17:41:12.091 - Using existing connection for thread 13396
2025-07-17 17:41:12.147 - Database connection closed for thread 13396
2025-07-17 17:41:12.153 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:41:12.155 - New database connection created successfully for thread 13396
2025-07-17 17:41:12.164 - Database connection closed for thread 13396
2025-07-17 17:41:12.165 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:41:12.167 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.205 - Using existing connection for thread 13396
2025-07-17 17:42:53.274 - Database connection closed for thread 13396
2025-07-17 17:42:53.288 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.289 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.289 - Database connection closed for thread 13396
2025-07-17 17:42:53.290 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.291 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.294 - Database connection closed for thread 13396
2025-07-17 17:42:53.295 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.297 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.303 - Database connection closed for thread 13396
2025-07-17 17:42:53.305 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.307 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.311 - Database connection closed for thread 13396
2025-07-17 17:42:53.315 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.316 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.343 - Database connection closed for thread 13396
2025-07-17 17:42:53.344 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.372 - New database connection created successfully for thread 13396
2025-07-17 17:42:53.387 - Database connection closed for thread 13396
2025-07-17 17:42:53.392 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:42:53.393 - New database connection created successfully for thread 13396
2025-07-17 17:44:46.129 - Using existing connection for thread 12812
2025-07-17 17:44:46.224 - Using existing connection for thread 13396
2025-07-17 17:44:46.306 - Database connection closed for thread 13396
2025-07-17 17:44:46.307 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:44:46.310 - New database connection created successfully for thread 13396
2025-07-17 17:44:46.324 - Database connection closed for thread 13396
2025-07-17 17:44:46.325 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:44:46.328 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.360 - Using existing connection for thread 13396
2025-07-17 17:47:29.470 - Database connection closed for thread 13396
2025-07-17 17:47:29.484 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.485 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.487 - Database connection closed for thread 13396
2025-07-17 17:47:29.488 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.489 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.491 - Database connection closed for thread 13396
2025-07-17 17:47:29.493 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.494 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.502 - Database connection closed for thread 13396
2025-07-17 17:47:29.503 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.505 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.512 - Database connection closed for thread 13396
2025-07-17 17:47:29.513 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.517 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.530 - Database connection closed for thread 13396
2025-07-17 17:47:29.531 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.533 - New database connection created successfully for thread 13396
2025-07-17 17:47:29.538 - Database connection closed for thread 13396
2025-07-17 17:47:29.539 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:47:29.540 - New database connection created successfully for thread 13396
2025-07-17 17:49:07.019 - Using existing connection for thread 12812
2025-07-17 17:49:07.120 - Using existing connection for thread 13396
2025-07-17 17:49:07.227 - Database connection closed for thread 13396
2025-07-17 17:49:07.232 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:49:07.237 - New database connection created successfully for thread 13396
2025-07-17 17:49:07.245 - Database connection closed for thread 13396
2025-07-17 17:49:07.252 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:49:07.259 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.513 - Using existing connection for thread 13396
2025-07-17 17:50:45.594 - Database connection closed for thread 13396
2025-07-17 17:50:45.605 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.606 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.607 - Database connection closed for thread 13396
2025-07-17 17:50:45.609 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.610 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.612 - Database connection closed for thread 13396
2025-07-17 17:50:45.613 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.615 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.623 - Database connection closed for thread 13396
2025-07-17 17:50:45.626 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.627 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.633 - Database connection closed for thread 13396
2025-07-17 17:50:45.635 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.636 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.655 - Database connection closed for thread 13396
2025-07-17 17:50:45.658 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.661 - New database connection created successfully for thread 13396
2025-07-17 17:50:45.665 - Database connection closed for thread 13396
2025-07-17 17:50:45.666 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:50:45.667 - New database connection created successfully for thread 13396
2025-07-17 17:52:18.474 - Using existing connection for thread 12812
2025-07-17 17:52:18.507 - Using existing connection for thread 13396
2025-07-17 17:52:18.564 - Database connection closed for thread 13396
2025-07-17 17:52:18.569 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:52:18.570 - New database connection created successfully for thread 13396
2025-07-17 17:52:18.577 - Database connection closed for thread 13396
2025-07-17 17:52:18.578 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:52:18.580 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.690 - Using existing connection for thread 13396
2025-07-17 17:53:38.787 - Database connection closed for thread 13396
2025-07-17 17:53:38.801 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.802 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.803 - Database connection closed for thread 13396
2025-07-17 17:53:38.804 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.805 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.808 - Database connection closed for thread 13396
2025-07-17 17:53:38.809 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.811 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.817 - Database connection closed for thread 13396
2025-07-17 17:53:38.818 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.828 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.833 - Database connection closed for thread 13396
2025-07-17 17:53:38.834 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.835 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.859 - Database connection closed for thread 13396
2025-07-17 17:53:38.860 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.861 - New database connection created successfully for thread 13396
2025-07-17 17:53:38.864 - Database connection closed for thread 13396
2025-07-17 17:53:38.867 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:53:38.868 - New database connection created successfully for thread 13396
2025-07-17 17:56:12.895 - Using existing connection for thread 12812
2025-07-17 17:56:12.922 - Using existing connection for thread 13396
2025-07-17 17:56:13.019 - Database connection closed for thread 13396
2025-07-17 17:56:13.021 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:56:13.022 - New database connection created successfully for thread 13396
2025-07-17 17:56:13.046 - Database connection closed for thread 13396
2025-07-17 17:56:13.050 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:56:13.052 - New database connection created successfully for thread 13396
2025-07-17 17:58:38.992 - Using existing connection for thread 13396
2025-07-17 17:58:39.067 - Database connection closed for thread 13396
2025-07-17 17:58:39.080 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.080 - New database connection created successfully for thread 13396
2025-07-17 17:58:39.081 - Database connection closed for thread 13396
2025-07-17 17:58:39.082 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.082 - New database connection created successfully for thread 13396
2025-07-17 17:58:39.085 - Database connection closed for thread 13396
2025-07-17 17:58:39.086 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.087 - New database connection created successfully for thread 13396
2025-07-17 17:58:39.091 - Database connection closed for thread 13396
2025-07-17 17:58:39.093 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.095 - New database connection created successfully for thread 13396
2025-07-17 17:58:39.102 - Database connection closed for thread 13396
2025-07-17 17:58:39.104 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.104 - New database connection created successfully for thread 13396
2025-07-17 17:58:39.150 - Database connection closed for thread 13396
2025-07-17 17:58:39.151 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.151 - New database connection created successfully for thread 13396
2025-07-17 17:58:39.154 - Database connection closed for thread 13396
2025-07-17 17:58:39.155 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 17:58:39.155 - New database connection created successfully for thread 13396
2025-07-17 18:01:05.111 - Using existing connection for thread 12812
2025-07-17 18:01:05.194 - Using existing connection for thread 13396
2025-07-17 18:01:05.248 - Database connection closed for thread 13396
2025-07-17 18:01:05.251 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:01:05.262 - New database connection created successfully for thread 13396
2025-07-17 18:01:05.271 - Database connection closed for thread 13396
2025-07-17 18:01:05.275 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:01:05.278 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.510 - Using existing connection for thread 13396
2025-07-17 18:02:43.565 - Database connection closed for thread 13396
2025-07-17 18:02:43.580 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.581 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.581 - Database connection closed for thread 13396
2025-07-17 18:02:43.582 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.583 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.586 - Database connection closed for thread 13396
2025-07-17 18:02:43.588 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.589 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.593 - Database connection closed for thread 13396
2025-07-17 18:02:43.595 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.596 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.606 - Database connection closed for thread 13396
2025-07-17 18:02:43.607 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.609 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.632 - Database connection closed for thread 13396
2025-07-17 18:02:43.633 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.634 - New database connection created successfully for thread 13396
2025-07-17 18:02:43.637 - Database connection closed for thread 13396
2025-07-17 18:02:43.638 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:02:43.641 - New database connection created successfully for thread 13396
2025-07-17 18:05:56.469 - Using existing connection for thread 12812
2025-07-17 18:05:56.524 - Using existing connection for thread 13396
2025-07-17 18:05:56.624 - Database connection closed for thread 13396
2025-07-17 18:05:56.625 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:05:56.626 - New database connection created successfully for thread 13396
2025-07-17 18:05:56.645 - Database connection closed for thread 13396
2025-07-17 18:05:56.648 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:05:56.650 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.273 - Using existing connection for thread 13396
2025-07-17 18:08:16.357 - Database connection closed for thread 13396
2025-07-17 18:08:16.371 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.372 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.372 - Database connection closed for thread 13396
2025-07-17 18:08:16.373 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.374 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.377 - Database connection closed for thread 13396
2025-07-17 18:08:16.378 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.379 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.387 - Database connection closed for thread 13396
2025-07-17 18:08:16.389 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.390 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.396 - Database connection closed for thread 13396
2025-07-17 18:08:16.397 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.399 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.410 - Database connection closed for thread 13396
2025-07-17 18:08:16.412 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.413 - New database connection created successfully for thread 13396
2025-07-17 18:08:16.418 - Database connection closed for thread 13396
2025-07-17 18:08:16.419 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:08:16.420 - New database connection created successfully for thread 13396
2025-07-17 18:09:47.685 - Using existing connection for thread 12812
2025-07-17 18:09:47.764 - Using existing connection for thread 13396
2025-07-17 18:09:47.861 - Database connection closed for thread 13396
2025-07-17 18:09:47.863 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:09:47.864 - New database connection created successfully for thread 13396
2025-07-17 18:09:47.878 - Database connection closed for thread 13396
2025-07-17 18:09:47.891 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:09:47.893 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.657 - Using existing connection for thread 13396
2025-07-17 18:12:35.754 - Database connection closed for thread 13396
2025-07-17 18:12:35.772 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.773 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.773 - Database connection closed for thread 13396
2025-07-17 18:12:35.774 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.775 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.778 - Database connection closed for thread 13396
2025-07-17 18:12:35.779 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.780 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.785 - Database connection closed for thread 13396
2025-07-17 18:12:35.787 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.788 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.795 - Database connection closed for thread 13396
2025-07-17 18:12:35.796 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.796 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.806 - Database connection closed for thread 13396
2025-07-17 18:12:35.811 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.813 - New database connection created successfully for thread 13396
2025-07-17 18:12:35.818 - Database connection closed for thread 13396
2025-07-17 18:12:35.819 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:12:35.820 - New database connection created successfully for thread 13396
2025-07-17 18:14:31.390 - Using existing connection for thread 12812
2025-07-17 18:14:31.467 - Using existing connection for thread 13396
2025-07-17 18:14:31.552 - Database connection closed for thread 13396
2025-07-17 18:14:31.555 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:14:31.557 - New database connection created successfully for thread 13396
2025-07-17 18:14:31.573 - Database connection closed for thread 13396
2025-07-17 18:14:31.577 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:14:31.580 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.483 - Using existing connection for thread 13396
2025-07-17 18:16:15.563 - Database connection closed for thread 13396
2025-07-17 18:16:15.578 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.579 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.579 - Database connection closed for thread 13396
2025-07-17 18:16:15.580 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.580 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.583 - Database connection closed for thread 13396
2025-07-17 18:16:15.584 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.586 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.590 - Database connection closed for thread 13396
2025-07-17 18:16:15.592 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.593 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.599 - Database connection closed for thread 13396
2025-07-17 18:16:15.600 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.601 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.623 - Database connection closed for thread 13396
2025-07-17 18:16:15.624 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.626 - New database connection created successfully for thread 13396
2025-07-17 18:16:15.635 - Database connection closed for thread 13396
2025-07-17 18:16:15.640 - Creating new thread-specific database connection to data\stats.db for thread 13396
2025-07-17 18:16:15.641 - New database connection created successfully for thread 13396
2025-07-17 18:24:50.928 - ensure_database_exists called from thread 11756
2025-07-17 18:24:50.930 - Creating new thread-specific database connection to data\stats.db for thread 11756
2025-07-17 18:24:50.934 - New database connection created successfully for thread 11756
2025-07-17 18:24:51.057 - Stats database initialized successfully
2025-07-17 18:24:51.058 - ensure_database_exists called from thread 11756
2025-07-17 18:24:51.060 - Using existing connection for thread 11756
2025-07-17 18:24:51.061 - Stats database initialized successfully
2025-07-17 18:24:51.062 - ensure_database_exists called from thread 11756
2025-07-17 18:24:51.063 - Using existing connection for thread 11756
2025-07-17 18:24:51.064 - Stats database initialized successfully
2025-07-17 18:26:14.005 - Using existing connection for thread 11756
2025-07-17 18:26:14.016 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:26:14.017 - New database connection created successfully for thread 20724
2025-07-17 18:26:14.154 - Database connection closed for thread 20724
2025-07-17 18:26:14.156 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:26:14.158 - New database connection created successfully for thread 20724
2025-07-17 18:26:14.174 - Database connection closed for thread 20724
2025-07-17 18:26:14.177 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:26:14.186 - New database connection created successfully for thread 20724
2025-07-17 18:26:14.211 - get_summary_stats called from thread 20724
2025-07-17 18:26:14.212 - Using existing connection for thread 20724
2025-07-17 18:26:14.213 - Total earnings from database: 2402.0
2025-07-17 18:26:14.213 - Daily earnings from database: 724.0
2025-07-17 18:26:14.214 - Daily games from database: 25
2025-07-17 18:26:14.214 - Wallet balance from database: 0
2025-07-17 18:26:14.215 - Total games played from database: 65
2025-07-17 18:26:14.216 - Total winners from database: 65
2025-07-17 18:26:14.216 - Returning summary stats: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 18:27:36.675 - Creating new thread-specific database connection to data\stats.db for thread 10764
2025-07-17 18:27:36.676 - New database connection created successfully for thread 10764
2025-07-17 18:27:36.682 - Database connection closed for thread 10764
2025-07-17 18:27:36.684 - Creating new thread-specific database connection to data\stats.db for thread 10764
2025-07-17 18:27:36.686 - New database connection created successfully for thread 10764
2025-07-17 18:27:36.857 - Using existing connection for thread 20724
2025-07-17 18:28:16.489 - Using existing connection for thread 11756
2025-07-17 18:28:16.561 - Database connection closed for thread 20724
2025-07-17 18:28:16.563 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:28:16.564 - New database connection created successfully for thread 20724
2025-07-17 18:28:16.579 - Database connection closed for thread 20724
2025-07-17 18:28:16.580 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:28:16.581 - New database connection created successfully for thread 20724
2025-07-17 18:30:24.728 - Using existing connection for thread 11756
2025-07-17 18:30:24.783 - Using existing connection for thread 20724
2025-07-17 18:30:24.902 - Database connection closed for thread 20724
2025-07-17 18:30:24.909 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:30:24.910 - New database connection created successfully for thread 20724
2025-07-17 18:30:24.928 - Database connection closed for thread 20724
2025-07-17 18:30:24.929 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:30:24.930 - New database connection created successfully for thread 20724
2025-07-17 18:31:52.065 - Creating new thread-specific database connection to data\stats.db for thread 2852
2025-07-17 18:31:52.066 - New database connection created successfully for thread 2852
2025-07-17 18:31:52.072 - Database connection closed for thread 2852
2025-07-17 18:31:52.073 - Creating new thread-specific database connection to data\stats.db for thread 2852
2025-07-17 18:31:52.074 - New database connection created successfully for thread 2852
2025-07-17 18:31:52.227 - Using existing connection for thread 20724
2025-07-17 18:34:15.277 - Using existing connection for thread 11756
2025-07-17 18:34:15.366 - Using existing connection for thread 20724
2025-07-17 18:34:15.382 - Database connection closed for thread 20724
2025-07-17 18:34:15.382 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:34:15.383 - New database connection created successfully for thread 20724
2025-07-17 18:34:15.386 - Database connection closed for thread 20724
2025-07-17 18:34:15.387 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:34:15.387 - New database connection created successfully for thread 20724
2025-07-17 18:35:06.172 - Creating new thread-specific database connection to data\stats.db for thread 13220
2025-07-17 18:35:06.175 - New database connection created successfully for thread 13220
2025-07-17 18:35:06.185 - Database connection closed for thread 13220
2025-07-17 18:35:06.186 - Creating new thread-specific database connection to data\stats.db for thread 13220
2025-07-17 18:35:06.186 - New database connection created successfully for thread 13220
2025-07-17 18:35:06.242 - Using existing connection for thread 20724
2025-07-17 18:39:35.307 - Recreating expired connection for thread 11756
2025-07-17 18:39:35.401 - Using existing connection for thread 20724
2025-07-17 18:39:35.406 - Database connection closed for thread 20724
2025-07-17 18:39:35.406 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:39:35.407 - New database connection created successfully for thread 20724
2025-07-17 18:39:35.410 - Database connection closed for thread 20724
2025-07-17 18:39:35.411 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:39:35.412 - New database connection created successfully for thread 20724
2025-07-17 18:40:58.862 - Creating new thread-specific database connection to data\stats.db for thread 6860
2025-07-17 18:40:58.864 - New database connection created successfully for thread 6860
2025-07-17 18:40:58.870 - Database connection closed for thread 6860
2025-07-17 18:40:58.872 - Creating new thread-specific database connection to data\stats.db for thread 6860
2025-07-17 18:40:58.873 - New database connection created successfully for thread 6860
2025-07-17 18:40:59.011 - Using existing connection for thread 20724
2025-07-17 18:43:11.101 - Using existing connection for thread 11756
2025-07-17 18:43:11.121 - Using existing connection for thread 20724
2025-07-17 18:43:11.147 - Database connection closed for thread 20724
2025-07-17 18:43:11.150 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:43:11.151 - New database connection created successfully for thread 20724
2025-07-17 18:43:11.155 - Database connection closed for thread 20724
2025-07-17 18:43:11.156 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:43:11.157 - New database connection created successfully for thread 20724
2025-07-17 18:44:26.256 - Creating new thread-specific database connection to data\stats.db for thread 4308
2025-07-17 18:44:26.257 - New database connection created successfully for thread 4308
2025-07-17 18:44:26.263 - Database connection closed for thread 4308
2025-07-17 18:44:26.265 - Creating new thread-specific database connection to data\stats.db for thread 4308
2025-07-17 18:44:26.266 - New database connection created successfully for thread 4308
2025-07-17 18:44:26.392 - Using existing connection for thread 20724
2025-07-17 18:47:37.929 - Using existing connection for thread 11756
2025-07-17 18:47:37.942 - Using existing connection for thread 20724
2025-07-17 18:47:37.976 - Database connection closed for thread 20724
2025-07-17 18:47:37.980 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:47:37.985 - New database connection created successfully for thread 20724
2025-07-17 18:47:37.990 - Database connection closed for thread 20724
2025-07-17 18:47:37.991 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:47:37.993 - New database connection created successfully for thread 20724
2025-07-17 18:48:49.518 - Creating new thread-specific database connection to data\stats.db for thread 14672
2025-07-17 18:48:49.519 - New database connection created successfully for thread 14672
2025-07-17 18:48:49.532 - Database connection closed for thread 14672
2025-07-17 18:48:49.534 - Creating new thread-specific database connection to data\stats.db for thread 14672
2025-07-17 18:48:49.535 - New database connection created successfully for thread 14672
2025-07-17 18:48:49.677 - Using existing connection for thread 20724
2025-07-17 18:51:23.875 - Using existing connection for thread 11756
2025-07-17 18:51:23.886 - Using existing connection for thread 20724
2025-07-17 18:51:23.914 - Database connection closed for thread 20724
2025-07-17 18:51:23.915 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:51:23.916 - New database connection created successfully for thread 20724
2025-07-17 18:51:23.921 - Database connection closed for thread 20724
2025-07-17 18:51:23.924 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:51:23.924 - New database connection created successfully for thread 20724
2025-07-17 18:52:47.666 - Creating new thread-specific database connection to data\stats.db for thread 16544
2025-07-17 18:52:47.667 - New database connection created successfully for thread 16544
2025-07-17 18:52:47.670 - Database connection closed for thread 16544
2025-07-17 18:52:47.672 - Creating new thread-specific database connection to data\stats.db for thread 16544
2025-07-17 18:52:47.672 - New database connection created successfully for thread 16544
2025-07-17 18:52:47.807 - Using existing connection for thread 20724
2025-07-17 18:56:06.395 - Using existing connection for thread 11756
2025-07-17 18:56:06.492 - Using existing connection for thread 20724
2025-07-17 18:56:06.497 - Database connection closed for thread 20724
2025-07-17 18:56:06.499 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:56:06.499 - New database connection created successfully for thread 20724
2025-07-17 18:56:06.502 - Database connection closed for thread 20724
2025-07-17 18:56:06.503 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:56:06.503 - New database connection created successfully for thread 20724
2025-07-17 18:57:51.032 - Creating new thread-specific database connection to data\stats.db for thread 16644
2025-07-17 18:57:51.034 - New database connection created successfully for thread 16644
2025-07-17 18:57:51.044 - Database connection closed for thread 16644
2025-07-17 18:57:51.046 - Creating new thread-specific database connection to data\stats.db for thread 16644
2025-07-17 18:57:51.047 - New database connection created successfully for thread 16644
2025-07-17 18:57:51.199 - Using existing connection for thread 20724
2025-07-17 18:59:55.558 - Using existing connection for thread 11756
2025-07-17 18:59:55.637 - Using existing connection for thread 20724
2025-07-17 18:59:55.643 - Database connection closed for thread 20724
2025-07-17 18:59:55.644 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:59:55.644 - New database connection created successfully for thread 20724
2025-07-17 18:59:55.647 - Database connection closed for thread 20724
2025-07-17 18:59:55.648 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 18:59:55.649 - New database connection created successfully for thread 20724
2025-07-17 19:01:32.550 - Creating new thread-specific database connection to data\stats.db for thread 4852
2025-07-17 19:01:32.551 - New database connection created successfully for thread 4852
2025-07-17 19:01:32.556 - Database connection closed for thread 4852
2025-07-17 19:01:32.557 - Creating new thread-specific database connection to data\stats.db for thread 4852
2025-07-17 19:01:32.559 - New database connection created successfully for thread 4852
2025-07-17 19:01:32.753 - Using existing connection for thread 20724
2025-07-17 19:03:53.467 - Using existing connection for thread 11756
2025-07-17 19:03:53.532 - Using existing connection for thread 20724
2025-07-17 19:03:53.549 - Database connection closed for thread 20724
2025-07-17 19:03:53.550 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:03:53.551 - New database connection created successfully for thread 20724
2025-07-17 19:03:53.557 - Database connection closed for thread 20724
2025-07-17 19:03:53.558 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:03:53.558 - New database connection created successfully for thread 20724
2025-07-17 19:04:49.005 - Creating new thread-specific database connection to data\stats.db for thread 20280
2025-07-17 19:04:49.007 - New database connection created successfully for thread 20280
2025-07-17 19:04:49.019 - Database connection closed for thread 20280
2025-07-17 19:04:49.027 - Creating new thread-specific database connection to data\stats.db for thread 20280
2025-07-17 19:04:49.029 - New database connection created successfully for thread 20280
2025-07-17 19:04:49.182 - Using existing connection for thread 20724
2025-07-17 19:09:13.755 - Recreating expired connection for thread 11756
2025-07-17 19:09:13.821 - Using existing connection for thread 20724
2025-07-17 19:09:13.825 - Database connection closed for thread 20724
2025-07-17 19:09:13.826 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:09:13.827 - New database connection created successfully for thread 20724
2025-07-17 19:09:13.830 - Database connection closed for thread 20724
2025-07-17 19:09:13.830 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:09:13.832 - New database connection created successfully for thread 20724
2025-07-17 19:10:17.330 - Creating new thread-specific database connection to data\stats.db for thread 3456
2025-07-17 19:10:17.331 - New database connection created successfully for thread 3456
2025-07-17 19:10:17.344 - Database connection closed for thread 3456
2025-07-17 19:10:17.346 - Creating new thread-specific database connection to data\stats.db for thread 3456
2025-07-17 19:10:17.347 - New database connection created successfully for thread 3456
2025-07-17 19:10:17.552 - Using existing connection for thread 20724
2025-07-17 19:11:49.797 - Using existing connection for thread 11756
2025-07-17 19:11:49.817 - Using existing connection for thread 20724
2025-07-17 19:11:49.837 - Database connection closed for thread 20724
2025-07-17 19:11:49.840 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:11:49.841 - New database connection created successfully for thread 20724
2025-07-17 19:11:49.845 - Database connection closed for thread 20724
2025-07-17 19:11:49.847 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:11:49.848 - New database connection created successfully for thread 20724
2025-07-17 19:13:27.244 - Creating new thread-specific database connection to data\stats.db for thread 2296
2025-07-17 19:13:27.246 - New database connection created successfully for thread 2296
2025-07-17 19:13:27.251 - Database connection closed for thread 2296
2025-07-17 19:13:27.256 - Creating new thread-specific database connection to data\stats.db for thread 2296
2025-07-17 19:13:27.260 - New database connection created successfully for thread 2296
2025-07-17 19:13:27.439 - Using existing connection for thread 20724
2025-07-17 19:16:45.699 - Using existing connection for thread 11756
2025-07-17 19:16:45.700 - Using existing connection for thread 20724
2025-07-17 19:16:45.714 - Database connection closed for thread 20724
2025-07-17 19:16:45.717 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:16:45.725 - New database connection created successfully for thread 20724
2025-07-17 19:16:45.731 - Database connection closed for thread 20724
2025-07-17 19:16:45.732 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:16:45.733 - New database connection created successfully for thread 20724
2025-07-17 19:18:13.877 - Creating new thread-specific database connection to data\stats.db for thread 15936
2025-07-17 19:18:13.878 - New database connection created successfully for thread 15936
2025-07-17 19:18:13.882 - Database connection closed for thread 15936
2025-07-17 19:18:13.889 - Creating new thread-specific database connection to data\stats.db for thread 15936
2025-07-17 19:18:13.890 - New database connection created successfully for thread 15936
2025-07-17 19:18:14.052 - Using existing connection for thread 20724
2025-07-17 19:20:57.509 - Using existing connection for thread 11756
2025-07-17 19:20:57.601 - Using existing connection for thread 20724
2025-07-17 19:20:57.624 - Database connection closed for thread 20724
2025-07-17 19:20:57.625 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:20:57.625 - New database connection created successfully for thread 20724
2025-07-17 19:20:57.629 - Database connection closed for thread 20724
2025-07-17 19:20:57.631 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:20:57.633 - New database connection created successfully for thread 20724
2025-07-17 19:23:00.250 - Creating new thread-specific database connection to data\stats.db for thread 14828
2025-07-17 19:23:00.251 - New database connection created successfully for thread 14828
2025-07-17 19:23:00.257 - Database connection closed for thread 14828
2025-07-17 19:23:00.262 - Creating new thread-specific database connection to data\stats.db for thread 14828
2025-07-17 19:23:00.267 - New database connection created successfully for thread 14828
2025-07-17 19:23:00.401 - Using existing connection for thread 20724
2025-07-17 19:28:14.998 - Recreating expired connection for thread 11756
2025-07-17 19:28:15.070 - Recreating expired connection for thread 20724
2025-07-17 19:28:15.201 - Database connection closed for thread 20724
2025-07-17 19:28:15.201 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:28:15.202 - New database connection created successfully for thread 20724
2025-07-17 19:28:15.217 - Database connection closed for thread 20724
2025-07-17 19:28:15.219 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:28:15.220 - New database connection created successfully for thread 20724
2025-07-17 19:28:53.649 - Creating new thread-specific database connection to data\stats.db for thread 2040
2025-07-17 19:28:53.650 - New database connection created successfully for thread 2040
2025-07-17 19:28:53.655 - Database connection closed for thread 2040
2025-07-17 19:28:53.657 - Creating new thread-specific database connection to data\stats.db for thread 2040
2025-07-17 19:28:53.658 - New database connection created successfully for thread 2040
2025-07-17 19:28:53.777 - Using existing connection for thread 20724
2025-07-17 19:31:36.035 - Using existing connection for thread 11756
2025-07-17 19:31:36.117 - Using existing connection for thread 20724
2025-07-17 19:31:36.123 - Database connection closed for thread 20724
2025-07-17 19:31:36.125 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:31:36.125 - New database connection created successfully for thread 20724
2025-07-17 19:31:36.129 - Database connection closed for thread 20724
2025-07-17 19:31:36.129 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:31:36.130 - New database connection created successfully for thread 20724
2025-07-17 19:33:51.239 - Creating new thread-specific database connection to data\stats.db for thread 3036
2025-07-17 19:33:51.241 - New database connection created successfully for thread 3036
2025-07-17 19:33:51.247 - Database connection closed for thread 3036
2025-07-17 19:33:51.250 - Creating new thread-specific database connection to data\stats.db for thread 3036
2025-07-17 19:33:51.251 - New database connection created successfully for thread 3036
2025-07-17 19:33:51.435 - Using existing connection for thread 20724
2025-07-17 19:36:41.579 - Recreating expired connection for thread 11756
2025-07-17 19:36:41.636 - Using existing connection for thread 20724
2025-07-17 19:36:41.650 - Database connection closed for thread 20724
2025-07-17 19:36:41.651 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:36:41.652 - New database connection created successfully for thread 20724
2025-07-17 19:36:41.656 - Database connection closed for thread 20724
2025-07-17 19:36:41.658 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:36:41.659 - New database connection created successfully for thread 20724
2025-07-17 19:37:20.294 - Creating new thread-specific database connection to data\stats.db for thread 12020
2025-07-17 19:37:20.295 - New database connection created successfully for thread 12020
2025-07-17 19:37:20.300 - Database connection closed for thread 12020
2025-07-17 19:37:20.303 - Creating new thread-specific database connection to data\stats.db for thread 12020
2025-07-17 19:37:20.306 - New database connection created successfully for thread 12020
2025-07-17 19:37:20.451 - Using existing connection for thread 20724
2025-07-17 19:41:39.992 - Using existing connection for thread 11756
2025-07-17 19:41:39.999 - Using existing connection for thread 20724
2025-07-17 19:41:40.011 - Database connection closed for thread 20724
2025-07-17 19:41:40.013 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:41:40.014 - New database connection created successfully for thread 20724
2025-07-17 19:41:40.019 - Database connection closed for thread 20724
2025-07-17 19:41:40.022 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:41:40.023 - New database connection created successfully for thread 20724
2025-07-17 19:42:56.019 - Creating new thread-specific database connection to data\stats.db for thread 18996
2025-07-17 19:42:56.020 - New database connection created successfully for thread 18996
2025-07-17 19:42:56.025 - Database connection closed for thread 18996
2025-07-17 19:42:56.028 - Creating new thread-specific database connection to data\stats.db for thread 18996
2025-07-17 19:42:56.033 - New database connection created successfully for thread 18996
2025-07-17 19:42:56.182 - Using existing connection for thread 20724
2025-07-17 19:44:42.564 - Using existing connection for thread 11756
2025-07-17 19:44:42.656 - Using existing connection for thread 20724
2025-07-17 19:44:42.660 - Database connection closed for thread 20724
2025-07-17 19:44:42.661 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:44:42.662 - New database connection created successfully for thread 20724
2025-07-17 19:44:42.666 - Database connection closed for thread 20724
2025-07-17 19:44:42.667 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:44:42.669 - New database connection created successfully for thread 20724
2025-07-17 19:46:24.148 - Creating new thread-specific database connection to data\stats.db for thread 14224
2025-07-17 19:46:24.149 - New database connection created successfully for thread 14224
2025-07-17 19:46:24.153 - Database connection closed for thread 14224
2025-07-17 19:46:24.155 - Creating new thread-specific database connection to data\stats.db for thread 14224
2025-07-17 19:46:24.156 - New database connection created successfully for thread 14224
2025-07-17 19:46:24.299 - Using existing connection for thread 20724
2025-07-17 19:49:34.800 - Using existing connection for thread 11756
2025-07-17 19:49:34.857 - Using existing connection for thread 20724
2025-07-17 19:49:34.862 - Database connection closed for thread 20724
2025-07-17 19:49:34.864 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:49:34.865 - New database connection created successfully for thread 20724
2025-07-17 19:49:34.867 - Database connection closed for thread 20724
2025-07-17 19:49:34.869 - Creating new thread-specific database connection to data\stats.db for thread 20724
2025-07-17 19:49:34.870 - New database connection created successfully for thread 20724
2025-07-17 19:50:14.317 - Creating new thread-specific database connection to data\stats.db for thread 11008
2025-07-17 19:50:14.317 - New database connection created successfully for thread 11008
2025-07-17 19:50:14.322 - Database connection closed for thread 11008
2025-07-17 19:50:14.324 - Creating new thread-specific database connection to data\stats.db for thread 11008
2025-07-17 19:50:14.325 - New database connection created successfully for thread 11008
2025-07-17 19:50:14.484 - Using existing connection for thread 20724
2025-07-17 20:02:45.234 - ensure_database_exists called from thread 20180
2025-07-17 20:02:45.235 - Creating new thread-specific database connection to data\stats.db for thread 20180
2025-07-17 20:02:45.243 - New database connection created successfully for thread 20180
2025-07-17 20:02:45.244 - Stats database initialized successfully
2025-07-17 20:02:45.247 - ensure_database_exists called from thread 20180
2025-07-17 20:02:45.248 - Using existing connection for thread 20180
2025-07-17 20:02:45.249 - Stats database initialized successfully
2025-07-17 20:02:45.249 - ensure_database_exists called from thread 20180
2025-07-17 20:02:45.250 - Using existing connection for thread 20180
2025-07-17 20:02:45.250 - Stats database initialized successfully
2025-07-17 20:04:13.423 - Using existing connection for thread 20180
2025-07-17 20:04:13.448 - Creating new thread-specific database connection to data\stats.db for thread 16848
2025-07-17 20:04:13.451 - New database connection created successfully for thread 16848
2025-07-17 20:04:13.607 - Database connection closed for thread 16848
2025-07-17 20:04:13.610 - Creating new thread-specific database connection to data\stats.db for thread 16848
2025-07-17 20:04:13.621 - New database connection created successfully for thread 16848
2025-07-17 20:04:13.629 - Database connection closed for thread 16848
2025-07-17 20:04:13.631 - Creating new thread-specific database connection to data\stats.db for thread 16848
2025-07-17 20:04:13.633 - New database connection created successfully for thread 16848
2025-07-17 20:04:13.646 - get_summary_stats called from thread 16848
2025-07-17 20:04:13.647 - Using existing connection for thread 16848
2025-07-17 20:04:13.648 - Total earnings from database: 2402.0
2025-07-17 20:04:13.649 - Daily earnings from database: 724.0
2025-07-17 20:04:13.649 - Daily games from database: 25
2025-07-17 20:04:13.649 - Wallet balance from database: 0
2025-07-17 20:04:13.651 - Total games played from database: 65
2025-07-17 20:04:13.651 - Total winners from database: 65
2025-07-17 20:04:13.653 - Returning summary stats: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 20:05:42.687 - Using existing connection for thread 20180
2025-07-17 20:05:42.742 - Using existing connection for thread 16848
2025-07-17 20:05:42.877 - Database connection closed for thread 16848
2025-07-17 20:05:42.879 - Creating new thread-specific database connection to data\stats.db for thread 16848
2025-07-17 20:05:42.881 - New database connection created successfully for thread 16848
2025-07-17 20:05:42.892 - Database connection closed for thread 16848
2025-07-17 20:05:42.894 - Creating new thread-specific database connection to data\stats.db for thread 16848
2025-07-17 20:05:42.897 - New database connection created successfully for thread 16848
2025-07-17 20:07:53.105 - Creating new thread-specific database connection to data\stats.db for thread 15676
2025-07-17 20:07:53.105 - New database connection created successfully for thread 15676
2025-07-17 20:07:53.136 - Database connection closed for thread 15676
2025-07-17 20:07:53.140 - Creating new thread-specific database connection to data\stats.db for thread 15676
2025-07-17 20:07:53.142 - New database connection created successfully for thread 15676
2025-07-17 20:07:53.280 - Using existing connection for thread 16848
2025-07-17 20:08:05.379 - get_weekly_stats called from thread 18252
2025-07-17 20:08:05.381 - Creating new thread-specific database connection to data\stats.db for thread 18252
2025-07-17 20:08:05.382 - New database connection created successfully for thread 18252
2025-07-17 20:08:05.383 - Getting weekly stats from 2025-07-11 to 2025-07-17
2025-07-17 20:08:05.432 - Query returned 7 rows
2025-07-17 20:08:05.434 - Row from database: 2025-07-11 - 0 games, 0 earnings
2025-07-17 20:08:05.437 - Row from database: 2025-07-12 - 0 games, 0 earnings
2025-07-17 20:08:05.438 - Row from database: 2025-07-13 - 0 games, 0 earnings
2025-07-17 20:08:05.441 - Row from database: 2025-07-14 - 0 games, 0 earnings
2025-07-17 20:08:05.442 - Row from database: 2025-07-15 - 0 games, 0 earnings
2025-07-17 20:08:05.443 - Row from database: 2025-07-16 - 39 games, 1672.0 earnings
2025-07-17 20:08:05.443 - Row from database: 2025-07-17 - 25 games, 724.0 earnings
2025-07-17 20:08:05.444 - Final weekly stats: [{'date': '2025-07-11', 'games_played': 0, 'earnings': 0, 'winners': 0, 'total_players': 0}, {'date': '2025-07-12', 'games_played': 0, 'earnings': 0, 'winners': 0, 'total_players': 0}, {'date': '2025-07-13', 'games_played': 0, 'earnings': 0, 'winners': 0, 'total_players': 0}, {'date': '2025-07-14', 'games_played': 0, 'earnings': 0, 'winners': 0, 'total_players': 0}, {'date': '2025-07-15', 'games_played': 0, 'earnings': 0, 'winners': 0, 'total_players': 0}, {'date': '2025-07-16', 'games_played': 39, 'earnings': 1672.0, 'winners': 39, 'total_players': 418}, {'date': '2025-07-17', 'games_played': 25, 'earnings': 724.0, 'winners': 25, 'total_players': 181}]
2025-07-17 20:08:05.449 - get_total_earnings called from thread 18252
2025-07-17 20:08:05.451 - Using existing connection for thread 18252
2025-07-17 20:08:05.452 - Total earnings from database: 2402.0
2025-07-17 20:08:05.453 - get_daily_earnings called from thread 18252
2025-07-17 20:08:05.453 - Using existing connection for thread 18252
2025-07-17 20:08:05.454 - Daily earnings from database: 724.0
2025-07-17 20:08:05.455 - get_daily_games_played called from thread 18252
2025-07-17 20:08:05.456 - Using existing connection for thread 18252
2025-07-17 20:08:05.457 - Daily games from database: 25
2025-07-17 20:08:05.458 - get_wallet_balance called from thread 18252
2025-07-17 20:08:05.458 - Using existing connection for thread 18252
2025-07-17 20:08:05.459 - Wallet balance from database: 2402.0
2025-07-17 20:08:05.460 - get_game_history called from thread 18252
2025-07-17 20:08:05.463 - Using existing connection for thread 18252
2025-07-17 20:08:05.464 - CRITICAL: Found 65 records with future dates in game_history - fixing them
2025-07-17 20:08:05.467 - CRITICAL: Updated date_time in game_history: 2025-06-13 13:14:00 -> 2025-07-17 20:08:05
2025-07-17 20:08:05.468 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:12:39 -> 2025-07-17 19:08:05
2025-07-17 20:08:05.469 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:19:16 -> 2025-07-17 18:08:05
2025-07-17 20:08:05.471 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:24:07 -> 2025-07-17 17:08:05
2025-07-17 20:08:05.472 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:28:41 -> 2025-07-17 16:08:05
2025-07-17 20:08:05.473 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:33:34 -> 2025-07-17 15:08:05
2025-07-17 20:08:05.474 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:37:48 -> 2025-07-17 14:08:05
2025-07-17 20:08:05.483 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:44:02 -> 2025-07-17 13:08:05
2025-07-17 20:08:05.483 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:50:29 -> 2025-07-17 12:08:05
2025-07-17 20:08:05.484 - CRITICAL: Updated date_time in game_history: 2025-07-16 17:59:06 -> 2025-07-17 11:08:05
2025-07-17 20:08:05.489 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:05:59 -> 2025-07-17 10:08:05
2025-07-17 20:08:05.492 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:12:35 -> 2025-07-17 09:08:05
2025-07-17 20:08:05.499 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:16:52 -> 2025-07-17 08:08:05
2025-07-17 20:08:05.499 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:21:52 -> 2025-07-17 07:08:05
2025-07-17 20:08:05.500 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:26:08 -> 2025-07-17 06:08:05
2025-07-17 20:08:05.501 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:30:57 -> 2025-07-17 05:08:05
2025-07-17 20:08:05.501 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:36:59 -> 2025-07-17 04:08:05
2025-07-17 20:08:05.502 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:42:06 -> 2025-07-17 03:08:05
2025-07-17 20:08:05.503 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:43:33 -> 2025-07-17 02:08:05
2025-07-17 20:08:05.504 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:51:05 -> 2025-07-17 01:08:05
2025-07-17 20:08:05.505 - CRITICAL: Updated date_time in game_history: 2025-07-16 18:57:35 -> 2025-07-17 00:08:05
2025-07-17 20:08:05.506 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:01:21 -> 2025-07-16 23:08:05
2025-07-17 20:08:05.509 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:06:47 -> 2025-07-16 22:08:05
2025-07-17 20:08:05.510 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:12:43 -> 2025-07-16 21:08:05
2025-07-17 20:08:05.510 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:18:20 -> 2025-07-16 20:08:05
2025-07-17 20:08:05.512 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:22:57 -> 2025-07-16 19:08:05
2025-07-17 20:08:05.513 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:27:59 -> 2025-07-16 18:08:05
2025-07-17 20:08:05.513 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:32:39 -> 2025-07-16 17:08:05
2025-07-17 20:08:05.514 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:37:18 -> 2025-07-16 16:08:05
2025-07-17 20:08:05.515 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:40:37 -> 2025-07-16 15:08:05
2025-07-17 20:08:05.515 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:46:22 -> 2025-07-16 14:08:05
2025-07-17 20:08:05.516 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:52:11 -> 2025-07-16 13:08:05
2025-07-17 20:08:05.517 - CRITICAL: Updated date_time in game_history: 2025-07-16 19:56:47 -> 2025-07-16 12:08:05
2025-07-17 20:08:05.517 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:00:19 -> 2025-07-16 11:08:05
2025-07-17 20:08:05.518 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:05:47 -> 2025-07-16 10:08:05
2025-07-17 20:08:05.519 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:09:48 -> 2025-07-16 09:08:05
2025-07-17 20:08:05.520 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:16:31 -> 2025-07-16 08:08:05
2025-07-17 20:08:05.520 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:23:56 -> 2025-07-16 07:08:05
2025-07-17 20:08:05.521 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:30:36 -> 2025-07-16 06:08:05
2025-07-17 20:08:05.522 - CRITICAL: Updated date_time in game_history: 2025-07-16 20:35:10 -> 2025-07-16 05:08:05
2025-07-17 20:08:05.526 - CRITICAL: Updated date_time in game_history: 2025-07-17 16:34:58 -> 2025-07-16 04:08:05
2025-07-17 20:08:05.527 - CRITICAL: Updated date_time in game_history: 2025-07-17 16:39:00 -> 2025-07-16 03:08:05
2025-07-17 20:08:05.527 - CRITICAL: Updated date_time in game_history: 2025-07-17 16:43:14 -> 2025-07-16 02:08:05
2025-07-17 20:08:05.528 - CRITICAL: Updated date_time in game_history: 2025-07-17 16:47:44 -> 2025-07-16 01:08:05
2025-07-17 20:08:05.528 - CRITICAL: Updated date_time in game_history: 2025-07-17 16:51:44 -> 2025-07-16 00:08:05
2025-07-17 20:08:05.529 - CRITICAL: Updated date_time in game_history: 2025-07-17 16:57:30 -> 2025-07-15 23:08:05
2025-07-17 20:08:05.529 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:02:44 -> 2025-07-15 22:08:05
2025-07-17 20:08:05.529 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:08:13 -> 2025-07-15 21:08:05
2025-07-17 20:08:05.529 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:12:23 -> 2025-07-15 20:08:05
2025-07-17 20:08:05.530 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:15:36 -> 2025-07-15 19:08:05
2025-07-17 20:08:05.532 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:19:23 -> 2025-07-15 18:08:05
2025-07-17 20:08:05.536 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:23:12 -> 2025-07-15 17:08:05
2025-07-17 20:08:05.540 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:28:07 -> 2025-07-15 16:08:05
2025-07-17 20:08:05.545 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:32:51 -> 2025-07-15 15:08:05
2025-07-17 20:08:05.545 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:35:37 -> 2025-07-15 14:08:05
2025-07-17 20:08:05.546 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:39:07 -> 2025-07-15 13:08:05
2025-07-17 20:08:05.547 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:42:53 -> 2025-07-15 12:08:05
2025-07-17 20:08:05.550 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:47:29 -> 2025-07-15 11:08:05
2025-07-17 20:08:05.552 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:50:45 -> 2025-07-15 10:08:05
2025-07-17 20:08:05.553 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:53:38 -> 2025-07-15 09:08:05
2025-07-17 20:08:05.556 - CRITICAL: Updated date_time in game_history: 2025-07-17 17:58:38 -> 2025-07-15 08:08:05
2025-07-17 20:08:05.557 - CRITICAL: Updated date_time in game_history: 2025-07-17 18:02:43 -> 2025-07-15 07:08:05
2025-07-17 20:08:05.558 - CRITICAL: Updated date_time in game_history: 2025-07-17 18:08:16 -> 2025-07-15 06:08:05
2025-07-17 20:08:05.559 - CRITICAL: Updated date_time in game_history: 2025-07-17 18:12:35 -> 2025-07-15 05:08:05
2025-07-17 20:08:05.560 - CRITICAL: Updated date_time in game_history: 2025-07-17 18:16:15 -> 2025-07-15 04:08:05
2025-07-17 20:08:05.632 - CRITICAL: Updated game_history date_times to current dates
2025-07-17 20:08:05.633 - Total games: 65, total pages: 7
2025-07-17 20:08:05.633 - Query returned 10 rows
2025-07-17 20:08:05.634 - Game record: ID=316, Date=2025-07-17 20:08:05, Winner=Game Reset, Prize=24.0, Tips=0.0
2025-07-17 20:08:05.634 - Game record: ID=317, Date=2025-07-17 19:08:05, Winner=Game Reset, Prize=48.0, Tips=0.0
2025-07-17 20:08:05.635 - Game record: ID=318, Date=2025-07-17 18:08:05, Winner=Game Reset, Prize=112.0, Tips=0.0
2025-07-17 20:08:05.635 - Game record: ID=319, Date=2025-07-17 17:08:05, Winner=Game Reset, Prize=112.0, Tips=0.0
2025-07-17 20:08:05.635 - Game record: ID=320, Date=2025-07-17 16:08:05, Winner=Game Reset, Prize=128.0, Tips=0.0
2025-07-17 20:08:05.636 - Game record: ID=321, Date=2025-07-17 15:08:05, Winner=Game Reset, Prize=112.0, Tips=0.0
2025-07-17 20:08:05.636 - Game record: ID=322, Date=2025-07-17 14:08:05, Winner=Game Reset, Prize=128.0, Tips=0.0
2025-07-17 20:08:05.637 - Game record: ID=323, Date=2025-07-17 13:08:05, Winner=Game Reset, Prize=112.0, Tips=0.0
2025-07-17 20:08:05.637 - Game record: ID=324, Date=2025-07-17 12:08:05, Winner=Game Reset, Prize=128.0, Tips=0.0
2025-07-17 20:08:05.638 - Game record: ID=325, Date=2025-07-17 11:08:05, Winner=Game Reset, Prize=176.0, Tips=0.0
2025-07-17 20:08:05.638 - Returning 10 game history records
2025-07-17 20:08:05.639 - First record: {'id': 316, 'date_time': '2025-07-17 20:08:05', 'username': 'Game Reset', 'house': 'Main House', 'stake': 10.0, 'players': 3, 'total_calls': 0, 'commission_percent': 20.0, 'fee': 6.0, 'total_prize': 24.0, 'details': '{"winner_name": "Game Reset", "winner_cartella": 0, "claim_type": "Reset", "game_duration": 0, "player_count": 3, "prize_amount": 24, "commission_percentage": 20.0, "called_numbers": [], "is_demo_mode": false, "date_time": "2025-06-13 13:14:00", "stake": 10, "bet_amount": 10, "timestamp": "2025-06-13T13:14:00.564528"}', 'status': 'Won', 'tips': 0.0}
2025-07-17 20:09:25.981 - ensure_database_exists called from thread 13048
2025-07-17 20:09:25.983 - Creating new thread-specific database connection to data\stats.db for thread 13048
2025-07-17 20:09:25.984 - New database connection created successfully for thread 13048
2025-07-17 20:09:25.987 - Stats database initialized successfully
2025-07-17 20:09:25.988 - ensure_database_exists called from thread 13048
2025-07-17 20:09:25.988 - Using existing connection for thread 13048
2025-07-17 20:09:25.989 - Stats database initialized successfully
2025-07-17 20:09:25.990 - ensure_database_exists called from thread 13048
2025-07-17 20:09:25.992 - Using existing connection for thread 13048
2025-07-17 20:09:25.993 - Stats database initialized successfully
2025-07-17 21:18:07.489 - ensure_database_exists called from thread 8368
2025-07-17 21:18:07.490 - Creating new thread-specific database connection to data\stats.db for thread 8368
2025-07-17 21:18:07.538 - New database connection created successfully for thread 8368
2025-07-17 21:18:07.542 - Stats database initialized successfully
2025-07-17 21:18:07.544 - ensure_database_exists called from thread 8368
2025-07-17 21:18:07.547 - Using existing connection for thread 8368
2025-07-17 21:18:07.549 - Stats database initialized successfully
2025-07-17 21:18:07.552 - ensure_database_exists called from thread 8368
2025-07-17 21:18:07.553 - Using existing connection for thread 8368
2025-07-17 21:18:07.553 - Stats database initialized successfully
2025-07-18 01:00:51.692 - ensure_database_exists called from thread 10044
2025-07-18 01:00:51.694 - Creating new thread-specific database connection to data\stats.db for thread 10044
2025-07-18 01:00:51.697 - New database connection created successfully for thread 10044
2025-07-18 01:00:51.701 - Stats database initialized successfully
2025-07-18 01:00:51.702 - ensure_database_exists called from thread 10044
2025-07-18 01:00:51.705 - Using existing connection for thread 10044
2025-07-18 01:00:51.706 - Stats database initialized successfully
2025-07-18 01:00:51.708 - ensure_database_exists called from thread 10044
2025-07-18 01:00:51.709 - Using existing connection for thread 10044
2025-07-18 01:00:51.711 - Stats database initialized successfully
2025-07-18 01:14:29.461 - ensure_database_exists called from thread 7528
2025-07-18 01:14:29.465 - Creating new thread-specific database connection to data\stats.db for thread 7528
2025-07-18 01:14:29.478 - New database connection created successfully for thread 7528
2025-07-18 01:14:29.558 - Stats database initialized successfully
2025-07-18 01:14:29.591 - ensure_database_exists called from thread 7528
2025-07-18 01:14:29.625 - Using existing connection for thread 7528
2025-07-18 01:14:29.643 - Stats database initialized successfully
2025-07-18 01:15:41.106 - ensure_database_exists called from thread 5248
2025-07-18 01:15:41.108 - Creating new thread-specific database connection to data\stats.db for thread 5248
2025-07-18 01:15:41.111 - New database connection created successfully for thread 5248
2025-07-18 01:15:41.117 - Stats database initialized successfully
2025-07-18 01:15:41.119 - ensure_database_exists called from thread 5248
2025-07-18 01:15:41.121 - Using existing connection for thread 5248
2025-07-18 01:15:41.122 - Stats database initialized successfully
2025-07-18 01:15:41.123 - ensure_database_exists called from thread 5248
2025-07-18 01:15:41.129 - Using existing connection for thread 5248
2025-07-18 01:15:41.136 - Stats database initialized successfully
2025-07-18 01:30:27.976 - ensure_database_exists called from thread 5884
2025-07-18 01:30:27.977 - Creating new thread-specific database connection to data\stats.db for thread 5884
2025-07-18 01:30:27.989 - New database connection created successfully for thread 5884
2025-07-18 01:30:27.992 - Stats database initialized successfully
2025-07-18 01:30:27.995 - ensure_database_exists called from thread 5884
2025-07-18 01:30:27.995 - Using existing connection for thread 5884
2025-07-18 01:30:27.996 - Stats database initialized successfully
2025-07-18 01:30:27.997 - ensure_database_exists called from thread 5884
2025-07-18 01:30:27.999 - Using existing connection for thread 5884
2025-07-18 01:30:28.003 - Stats database initialized successfully
2025-07-18 01:50:46.596 - ensure_database_exists called from thread 1624
2025-07-18 01:50:46.719 - Creating new thread-specific database connection to data\stats.db for thread 1624
2025-07-18 01:50:46.721 - New database connection created successfully for thread 1624
2025-07-18 01:50:46.726 - Stats database initialized successfully
2025-07-18 01:50:46.727 - ensure_database_exists called from thread 1624
2025-07-18 01:50:46.728 - Using existing connection for thread 1624
2025-07-18 01:50:46.730 - Stats database initialized successfully
2025-07-18 01:50:46.730 - ensure_database_exists called from thread 1624
2025-07-18 01:50:46.731 - Using existing connection for thread 1624
2025-07-18 01:50:46.733 - Stats database initialized successfully
2025-07-18 01:54:51.448 - ensure_database_exists called from thread 3264
2025-07-18 01:54:51.451 - Creating new thread-specific database connection to data\stats.db for thread 3264
2025-07-18 01:54:51.454 - New database connection created successfully for thread 3264
2025-07-18 01:54:51.459 - Stats database initialized successfully
2025-07-18 01:54:51.461 - ensure_database_exists called from thread 3264
2025-07-18 01:54:51.462 - Using existing connection for thread 3264
2025-07-18 01:54:51.463 - Stats database initialized successfully
2025-07-18 01:54:51.464 - ensure_database_exists called from thread 3264
2025-07-18 01:54:51.465 - Using existing connection for thread 3264
2025-07-18 01:54:51.467 - Stats database initialized successfully
2025-07-18 02:10:11.709 - ensure_database_exists called from thread 8476
2025-07-18 02:10:11.712 - Creating new thread-specific database connection to data\stats.db for thread 8476
2025-07-18 02:10:11.717 - New database connection created successfully for thread 8476
2025-07-18 02:10:11.724 - Stats database initialized successfully
2025-07-18 02:10:11.725 - ensure_database_exists called from thread 8476
2025-07-18 02:10:11.727 - Using existing connection for thread 8476
2025-07-18 02:10:11.728 - Stats database initialized successfully
2025-07-18 02:11:23.309 - ensure_database_exists called from thread 8364
2025-07-18 02:11:23.311 - Creating new thread-specific database connection to data\stats.db for thread 8364
2025-07-18 02:11:23.313 - New database connection created successfully for thread 8364
2025-07-18 02:11:23.321 - Stats database initialized successfully
2025-07-18 02:11:23.322 - ensure_database_exists called from thread 8364
2025-07-18 02:11:23.323 - Using existing connection for thread 8364
2025-07-18 02:11:23.325 - Stats database initialized successfully
2025-07-18 02:11:23.326 - ensure_database_exists called from thread 8364
2025-07-18 02:11:23.327 - Using existing connection for thread 8364
2025-07-18 02:11:23.328 - Stats database initialized successfully
2025-07-18 02:16:56.047 - ensure_database_exists called from thread 14260
2025-07-18 02:16:56.051 - Creating new thread-specific database connection to data\stats.db for thread 14260
2025-07-18 02:16:56.054 - New database connection created successfully for thread 14260
2025-07-18 02:16:56.059 - Stats database initialized successfully
2025-07-18 02:16:56.060 - ensure_database_exists called from thread 14260
2025-07-18 02:16:56.061 - Using existing connection for thread 14260
2025-07-18 02:16:56.062 - Stats database initialized successfully
2025-07-18 02:21:20.579 - ensure_database_exists called from thread 8956
2025-07-18 02:21:20.589 - Creating new thread-specific database connection to data\stats.db for thread 8956
2025-07-18 02:21:20.590 - New database connection created successfully for thread 8956
2025-07-18 02:21:20.592 - Stats database initialized successfully
2025-07-18 02:21:20.593 - ensure_database_exists called from thread 8956
2025-07-18 02:21:20.594 - Using existing connection for thread 8956
2025-07-18 02:21:20.595 - Stats database initialized successfully
2025-07-18 02:22:08.054 - ensure_database_exists called from thread 13956
2025-07-18 02:22:08.055 - Creating new thread-specific database connection to data\stats.db for thread 13956
2025-07-18 02:22:08.057 - New database connection created successfully for thread 13956
2025-07-18 02:22:08.060 - Stats database initialized successfully
2025-07-18 02:22:08.061 - ensure_database_exists called from thread 13956
2025-07-18 02:22:08.062 - Using existing connection for thread 13956
2025-07-18 02:22:08.063 - Stats database initialized successfully
2025-07-18 02:22:08.064 - ensure_database_exists called from thread 13956
2025-07-18 02:22:08.069 - Using existing connection for thread 13956
2025-07-18 02:22:08.070 - Stats database initialized successfully
2025-07-18 03:52:34.733 - ensure_database_exists called from thread 5320
2025-07-18 03:52:34.737 - Creating new thread-specific database connection to data\stats.db for thread 5320
2025-07-18 03:52:34.738 - New database connection created successfully for thread 5320
2025-07-18 03:52:34.742 - Stats database initialized successfully
2025-07-18 03:52:34.748 - ensure_database_exists called from thread 5320
2025-07-18 03:52:34.750 - Using existing connection for thread 5320
2025-07-18 03:52:34.754 - Stats database initialized successfully
2025-07-18 04:01:09.598 - ensure_database_exists called from thread 14520
2025-07-18 04:01:09.610 - Creating new thread-specific database connection to data\stats.db for thread 14520
2025-07-18 04:01:09.612 - New database connection created successfully for thread 14520
2025-07-18 04:01:09.615 - Stats database initialized successfully
2025-07-18 04:01:09.625 - ensure_database_exists called from thread 14520
2025-07-18 04:01:09.632 - Using existing connection for thread 14520
2025-07-18 04:01:09.638 - Stats database initialized successfully
2025-07-18 04:04:28.646 - ensure_database_exists called from thread 9140
2025-07-18 04:04:28.652 - Creating new thread-specific database connection to data\stats.db for thread 9140
2025-07-18 04:04:28.687 - New database connection created successfully for thread 9140
2025-07-18 04:04:28.689 - Stats database initialized successfully
2025-07-18 04:04:28.691 - ensure_database_exists called from thread 9140
2025-07-18 04:04:28.694 - Using existing connection for thread 9140
2025-07-18 04:04:28.695 - Stats database initialized successfully
2025-07-18 04:04:28.700 - ensure_database_exists called from thread 9140
2025-07-18 04:04:28.701 - Using existing connection for thread 9140
2025-07-18 04:04:28.705 - Stats database initialized successfully
2025-07-18 04:05:50.686 - Using existing connection for thread 9140
2025-07-18 04:05:50.728 - Creating new thread-specific database connection to data\stats.db for thread 3028
2025-07-18 04:05:50.739 - New database connection created successfully for thread 3028
2025-07-18 04:05:50.819 - Database connection closed for thread 3028
2025-07-18 04:05:50.822 - Creating new thread-specific database connection to data\stats.db for thread 3028
2025-07-18 04:05:50.823 - New database connection created successfully for thread 3028
2025-07-18 04:05:50.839 - Database connection closed for thread 3028
2025-07-18 04:05:50.842 - Creating new thread-specific database connection to data\stats.db for thread 3028
2025-07-18 04:05:50.848 - New database connection created successfully for thread 3028
2025-07-18 04:05:50.867 - get_summary_stats called from thread 3028
2025-07-18 04:05:50.870 - Using existing connection for thread 3028
2025-07-18 04:05:50.872 - Total earnings from database: 1726.0
2025-07-18 04:05:50.874 - Daily earnings from database: 0.0
2025-07-18 04:05:50.878 - Daily games from database: 0
2025-07-18 04:05:50.883 - Wallet balance from database: 2402.0
2025-07-18 04:05:50.891 - Total games played from database: 65
2025-07-18 04:05:50.897 - Total winners from database: 65
2025-07-18 04:05:50.902 - Returning summary stats: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-19 23:23:03.531 - ensure_database_exists called from thread 13668
2025-07-19 23:23:03.568 - Creating new thread-specific database connection to data\stats.db for thread 13668
2025-07-19 23:23:03.724 - New database connection created successfully for thread 13668
2025-07-19 23:23:04.319 - Stats database initialized successfully
2025-07-19 23:23:04.323 - ensure_database_exists called from thread 13668
2025-07-19 23:23:04.324 - Using existing connection for thread 13668
2025-07-19 23:23:04.325 - Stats database initialized successfully
2025-07-19 23:23:04.327 - ensure_database_exists called from thread 13668
2025-07-19 23:23:04.329 - Using existing connection for thread 13668
2025-07-19 23:23:04.330 - Stats database initialized successfully
2025-07-19 23:30:25.683 - ensure_database_exists called from thread 7640
2025-07-19 23:30:25.686 - Creating new thread-specific database connection to data\stats.db for thread 7640
2025-07-19 23:30:25.689 - New database connection created successfully for thread 7640
2025-07-19 23:30:25.690 - Stats database initialized successfully
2025-07-19 23:30:25.691 - ensure_database_exists called from thread 7640
2025-07-19 23:30:25.692 - Using existing connection for thread 7640
2025-07-19 23:30:25.692 - Stats database initialized successfully
2025-07-19 23:30:41.080 - ensure_database_exists called from thread 7868
2025-07-19 23:30:41.081 - Creating new thread-specific database connection to data\stats.db for thread 7868
2025-07-19 23:30:41.084 - New database connection created successfully for thread 7868
2025-07-19 23:30:41.087 - Stats database initialized successfully
2025-07-19 23:30:41.088 - ensure_database_exists called from thread 7868
2025-07-19 23:30:41.089 - Using existing connection for thread 7868
2025-07-19 23:30:41.090 - Stats database initialized successfully
2025-07-19 23:32:09.297 - ensure_database_exists called from thread 12824
2025-07-19 23:32:09.298 - Creating new thread-specific database connection to data\stats.db for thread 12824
2025-07-19 23:32:09.301 - New database connection created successfully for thread 12824
2025-07-19 23:32:09.303 - Stats database initialized successfully
2025-07-19 23:32:09.304 - ensure_database_exists called from thread 12824
2025-07-19 23:32:09.305 - Using existing connection for thread 12824
2025-07-19 23:32:09.306 - Stats database initialized successfully
2025-07-19 23:32:27.709 - ensure_database_exists called from thread 11252
2025-07-19 23:32:27.711 - Creating new thread-specific database connection to data\stats.db for thread 11252
2025-07-19 23:32:27.712 - New database connection created successfully for thread 11252
2025-07-19 23:32:27.714 - Stats database initialized successfully
2025-07-19 23:32:27.716 - ensure_database_exists called from thread 11252
2025-07-19 23:32:27.718 - Using existing connection for thread 11252
2025-07-19 23:32:27.719 - Stats database initialized successfully
2025-07-19 23:32:58.307 - ensure_database_exists called from thread 8084
2025-07-19 23:32:58.311 - Creating new thread-specific database connection to data\stats.db for thread 8084
2025-07-19 23:32:58.313 - New database connection created successfully for thread 8084
2025-07-19 23:32:58.314 - Stats database initialized successfully
2025-07-19 23:32:58.315 - ensure_database_exists called from thread 8084
2025-07-19 23:32:58.316 - Using existing connection for thread 8084
2025-07-19 23:32:58.316 - Stats database initialized successfully
2025-07-19 23:33:25.602 - ensure_database_exists called from thread 13932
2025-07-19 23:33:25.604 - Creating new thread-specific database connection to data\stats.db for thread 13932
2025-07-19 23:33:25.604 - New database connection created successfully for thread 13932
2025-07-19 23:33:25.606 - Stats database initialized successfully
2025-07-19 23:33:25.609 - ensure_database_exists called from thread 13932
2025-07-19 23:33:25.612 - Using existing connection for thread 13932
2025-07-19 23:33:25.613 - Stats database initialized successfully
2025-07-19 23:34:12.112 - ensure_database_exists called from thread 6712
2025-07-19 23:34:12.116 - Creating new thread-specific database connection to data\stats.db for thread 6712
2025-07-19 23:34:12.119 - New database connection created successfully for thread 6712
2025-07-19 23:34:12.120 - Stats database initialized successfully
2025-07-19 23:34:12.121 - ensure_database_exists called from thread 6712
2025-07-19 23:34:12.123 - Using existing connection for thread 6712
2025-07-19 23:34:12.126 - Stats database initialized successfully
2025-07-19 23:35:10.175 - ensure_database_exists called from thread 9412
2025-07-19 23:35:10.176 - Creating new thread-specific database connection to data\stats.db for thread 9412
2025-07-19 23:35:10.187 - New database connection created successfully for thread 9412
2025-07-19 23:35:10.195 - Stats database initialized successfully
2025-07-19 23:35:10.200 - ensure_database_exists called from thread 9412
2025-07-19 23:35:10.203 - Using existing connection for thread 9412
2025-07-19 23:35:10.210 - Stats database initialized successfully
2025-07-19 23:35:10.217 - ensure_database_exists called from thread 9412
2025-07-19 23:35:10.219 - Using existing connection for thread 9412
2025-07-19 23:35:10.221 - Stats database initialized successfully
2025-07-19 23:47:34.918 - ensure_database_exists called from thread 9488
2025-07-19 23:47:34.921 - Creating new thread-specific database connection to data\stats.db for thread 9488
2025-07-19 23:47:34.923 - New database connection created successfully for thread 9488
2025-07-19 23:47:34.925 - Stats database initialized successfully
2025-07-19 23:47:34.926 - ensure_database_exists called from thread 9488
2025-07-19 23:47:34.926 - Using existing connection for thread 9488
2025-07-19 23:47:34.927 - Stats database initialized successfully
2025-07-19 23:48:37.500 - ensure_database_exists called from thread 3912
2025-07-19 23:48:37.502 - Creating new thread-specific database connection to data\stats.db for thread 3912
2025-07-19 23:48:37.504 - New database connection created successfully for thread 3912
2025-07-19 23:48:37.508 - Stats database initialized successfully
2025-07-19 23:48:37.509 - ensure_database_exists called from thread 3912
2025-07-19 23:48:37.510 - Using existing connection for thread 3912
2025-07-19 23:48:37.511 - Stats database initialized successfully
2025-07-19 23:49:36.956 - ensure_database_exists called from thread 10200
2025-07-19 23:49:36.958 - Creating new thread-specific database connection to data\stats.db for thread 10200
2025-07-19 23:49:36.963 - New database connection created successfully for thread 10200
2025-07-19 23:49:36.967 - Stats database initialized successfully
2025-07-19 23:49:36.973 - ensure_database_exists called from thread 10200
2025-07-19 23:49:36.974 - Using existing connection for thread 10200
2025-07-19 23:49:36.976 - Stats database initialized successfully
2025-07-19 23:51:00.318 - ensure_database_exists called from thread 14096
2025-07-19 23:51:00.323 - Creating new thread-specific database connection to data\stats.db for thread 14096
2025-07-19 23:51:00.324 - New database connection created successfully for thread 14096
2025-07-19 23:51:00.326 - Stats database initialized successfully
2025-07-19 23:51:00.326 - ensure_database_exists called from thread 14096
2025-07-19 23:51:00.327 - Using existing connection for thread 14096
2025-07-19 23:51:00.328 - Stats database initialized successfully
2025-07-19 23:51:47.830 - ensure_database_exists called from thread 12404
2025-07-19 23:51:47.831 - Creating new thread-specific database connection to data\stats.db for thread 12404
2025-07-19 23:51:47.839 - New database connection created successfully for thread 12404
2025-07-19 23:51:47.842 - Stats database initialized successfully
2025-07-19 23:51:47.845 - ensure_database_exists called from thread 12404
2025-07-19 23:51:47.846 - Using existing connection for thread 12404
2025-07-19 23:51:47.847 - Stats database initialized successfully
2025-07-19 23:51:47.848 - ensure_database_exists called from thread 12404
2025-07-19 23:51:47.849 - Using existing connection for thread 12404
2025-07-19 23:51:47.850 - Stats database initialized successfully
2025-07-19 23:55:29.429 - ensure_database_exists called from thread 2688
2025-07-19 23:55:29.461 - Creating new thread-specific database connection to data\stats.db for thread 2688
2025-07-19 23:55:29.516 - New database connection created successfully for thread 2688
2025-07-19 23:55:29.533 - Stats database initialized successfully
2025-07-19 23:55:29.564 - ensure_database_exists called from thread 2688
2025-07-19 23:55:29.565 - Using existing connection for thread 2688
2025-07-19 23:55:29.569 - Stats database initialized successfully
2025-07-19 23:56:58.362 - ensure_database_exists called from thread 12120
2025-07-19 23:56:58.366 - Creating new thread-specific database connection to data\stats.db for thread 12120
2025-07-19 23:56:58.367 - New database connection created successfully for thread 12120
2025-07-19 23:56:58.369 - Stats database initialized successfully
2025-07-19 23:56:58.369 - ensure_database_exists called from thread 12120
2025-07-19 23:56:58.370 - Using existing connection for thread 12120
2025-07-19 23:56:58.371 - Stats database initialized successfully
2025-07-19 23:58:11.328 - ensure_database_exists called from thread 10364
2025-07-19 23:58:11.331 - Creating new thread-specific database connection to data\stats.db for thread 10364
2025-07-19 23:58:11.334 - New database connection created successfully for thread 10364
2025-07-19 23:58:11.335 - Stats database initialized successfully
2025-07-19 23:58:11.336 - ensure_database_exists called from thread 10364
2025-07-19 23:58:11.337 - Using existing connection for thread 10364
2025-07-19 23:58:11.338 - Stats database initialized successfully
2025-07-19 23:59:34.899 - ensure_database_exists called from thread 8780
2025-07-19 23:59:34.902 - Creating new thread-specific database connection to data\stats.db for thread 8780
2025-07-19 23:59:34.903 - New database connection created successfully for thread 8780
2025-07-19 23:59:34.905 - Stats database initialized successfully
2025-07-19 23:59:34.906 - ensure_database_exists called from thread 8780
2025-07-19 23:59:34.907 - Using existing connection for thread 8780
2025-07-19 23:59:34.908 - Stats database initialized successfully
2025-07-20 00:00:16.399 - ensure_database_exists called from thread 6740
2025-07-20 00:00:16.400 - Creating new thread-specific database connection to data\stats.db for thread 6740
2025-07-20 00:00:16.411 - New database connection created successfully for thread 6740
2025-07-20 00:00:16.419 - Stats database initialized successfully
2025-07-20 00:00:16.422 - ensure_database_exists called from thread 6740
2025-07-20 00:00:16.422 - Using existing connection for thread 6740
2025-07-20 00:00:16.423 - Stats database initialized successfully
2025-07-20 00:00:16.424 - ensure_database_exists called from thread 6740
2025-07-20 00:00:16.425 - Using existing connection for thread 6740
2025-07-20 00:00:16.426 - Stats database initialized successfully
2025-07-20 00:01:12.873 - ensure_database_exists called from thread 2172
2025-07-20 00:01:12.874 - Creating new thread-specific database connection to data\stats.db for thread 2172
2025-07-20 00:01:12.875 - New database connection created successfully for thread 2172
2025-07-20 00:01:12.896 - Stats database initialized successfully
2025-07-20 00:01:12.899 - ensure_database_exists called from thread 2172
2025-07-20 00:01:12.900 - Using existing connection for thread 2172
2025-07-20 00:01:12.901 - Stats database initialized successfully
2025-07-20 00:01:12.902 - ensure_database_exists called from thread 2172
2025-07-20 00:01:12.903 - Using existing connection for thread 2172
2025-07-20 00:01:12.904 - Stats database initialized successfully
2025-07-20 00:10:14.141 - ensure_database_exists called from thread 2508
2025-07-20 00:10:14.151 - Creating new thread-specific database connection to data\stats.db for thread 2508
2025-07-20 00:10:14.152 - New database connection created successfully for thread 2508
2025-07-20 00:10:14.154 - Stats database initialized successfully
2025-07-20 00:10:14.155 - ensure_database_exists called from thread 2508
2025-07-20 00:10:14.155 - Using existing connection for thread 2508
2025-07-20 00:10:14.157 - Stats database initialized successfully
2025-07-20 00:11:17.593 - ensure_database_exists called from thread 14316
2025-07-20 00:11:17.594 - Creating new thread-specific database connection to data\stats.db for thread 14316
2025-07-20 00:11:17.602 - New database connection created successfully for thread 14316
2025-07-20 00:11:17.605 - Stats database initialized successfully
2025-07-20 00:11:17.609 - ensure_database_exists called from thread 14316
2025-07-20 00:11:17.610 - Using existing connection for thread 14316
2025-07-20 00:11:17.614 - Stats database initialized successfully
2025-07-20 00:11:17.617 - ensure_database_exists called from thread 14316
2025-07-20 00:11:17.618 - Using existing connection for thread 14316
2025-07-20 00:11:17.620 - Stats database initialized successfully
2025-07-20 00:16:59.231 - ensure_database_exists called from thread 9796
2025-07-20 00:16:59.233 - Creating new thread-specific database connection to data\stats.db for thread 9796
2025-07-20 00:16:59.236 - New database connection created successfully for thread 9796
2025-07-20 00:16:59.239 - Stats database initialized successfully
2025-07-20 00:16:59.240 - ensure_database_exists called from thread 9796
2025-07-20 00:16:59.241 - Using existing connection for thread 9796
2025-07-20 00:16:59.241 - Stats database initialized successfully
2025-07-20 00:18:10.597 - ensure_database_exists called from thread 8732
2025-07-20 00:18:10.603 - Creating new thread-specific database connection to data\stats.db for thread 8732
2025-07-20 00:18:10.604 - New database connection created successfully for thread 8732
2025-07-20 00:18:10.605 - Stats database initialized successfully
2025-07-20 00:18:10.607 - ensure_database_exists called from thread 8732
2025-07-20 00:18:10.607 - Using existing connection for thread 8732
2025-07-20 00:18:10.608 - Stats database initialized successfully
2025-07-20 00:23:21.829 - ensure_database_exists called from thread 5896
2025-07-20 00:23:21.832 - Creating new thread-specific database connection to data\stats.db for thread 5896
2025-07-20 00:23:21.845 - New database connection created successfully for thread 5896
2025-07-20 00:23:21.857 - Stats database initialized successfully
2025-07-20 00:23:21.861 - ensure_database_exists called from thread 5896
2025-07-20 00:23:21.864 - Using existing connection for thread 5896
2025-07-20 00:23:21.866 - Stats database initialized successfully
2025-07-20 00:23:21.868 - ensure_database_exists called from thread 5896
2025-07-20 00:23:21.876 - Using existing connection for thread 5896
2025-07-20 00:23:21.879 - Stats database initialized successfully
2025-07-20 00:25:10.262 - ensure_database_exists called from thread 9580
2025-07-20 00:25:10.263 - Creating new thread-specific database connection to data\stats.db for thread 9580
2025-07-20 00:25:10.267 - New database connection created successfully for thread 9580
2025-07-20 00:25:10.268 - Stats database initialized successfully
2025-07-20 00:25:10.269 - ensure_database_exists called from thread 9580
2025-07-20 00:25:10.270 - Using existing connection for thread 9580
2025-07-20 00:25:10.271 - Stats database initialized successfully
2025-07-20 00:26:45.523 - ensure_database_exists called from thread 13052
2025-07-20 00:26:45.524 - Creating new thread-specific database connection to data\stats.db for thread 13052
2025-07-20 00:26:45.535 - New database connection created successfully for thread 13052
2025-07-20 00:26:45.538 - Stats database initialized successfully
2025-07-20 00:26:45.541 - ensure_database_exists called from thread 13052
2025-07-20 00:26:45.542 - Using existing connection for thread 13052
2025-07-20 00:26:45.543 - Stats database initialized successfully
2025-07-20 00:26:45.544 - ensure_database_exists called from thread 13052
2025-07-20 00:26:45.545 - Using existing connection for thread 13052
2025-07-20 00:26:45.545 - Stats database initialized successfully
2025-07-20 00:28:31.345 - ensure_database_exists called from thread 904
2025-07-20 00:28:31.352 - Creating new thread-specific database connection to data\stats.db for thread 904
2025-07-20 00:28:31.354 - New database connection created successfully for thread 904
2025-07-20 00:28:31.361 - Stats database initialized successfully
2025-07-20 00:28:31.362 - ensure_database_exists called from thread 904
2025-07-20 00:28:31.363 - Using existing connection for thread 904
2025-07-20 00:28:31.365 - Stats database initialized successfully
2025-07-20 00:39:07.069 - ensure_database_exists called from thread 2532
2025-07-20 00:39:07.076 - Creating new thread-specific database connection to data\stats.db for thread 2532
2025-07-20 00:39:07.078 - New database connection created successfully for thread 2532
2025-07-20 00:39:07.083 - Stats database initialized successfully
2025-07-20 00:39:07.087 - ensure_database_exists called from thread 2532
2025-07-20 00:39:07.090 - Using existing connection for thread 2532
2025-07-20 00:39:07.093 - Stats database initialized successfully
2025-07-20 00:42:32.967 - ensure_database_exists called from thread 4864
2025-07-20 00:42:32.968 - Creating new thread-specific database connection to data\stats.db for thread 4864
2025-07-20 00:42:32.979 - New database connection created successfully for thread 4864
2025-07-20 00:42:32.982 - Stats database initialized successfully
2025-07-20 00:42:32.984 - ensure_database_exists called from thread 4864
2025-07-20 00:42:32.986 - Using existing connection for thread 4864
2025-07-20 00:42:32.987 - Stats database initialized successfully
2025-07-20 00:42:32.989 - ensure_database_exists called from thread 4864
2025-07-20 00:42:32.991 - Using existing connection for thread 4864
2025-07-20 00:42:32.992 - Stats database initialized successfully
2025-07-20 00:45:33.889 - ensure_database_exists called from thread 11848
2025-07-20 00:45:33.890 - Creating new thread-specific database connection to data\stats.db for thread 11848
2025-07-20 00:45:33.899 - New database connection created successfully for thread 11848
2025-07-20 00:45:33.903 - Stats database initialized successfully
2025-07-20 00:45:33.906 - ensure_database_exists called from thread 11848
2025-07-20 00:45:33.907 - Using existing connection for thread 11848
2025-07-20 00:45:33.908 - Stats database initialized successfully
2025-07-20 00:45:33.909 - ensure_database_exists called from thread 11848
2025-07-20 00:45:33.910 - Using existing connection for thread 11848
2025-07-20 00:45:33.911 - Stats database initialized successfully
2025-07-20 00:46:55.418 - ensure_database_exists called from thread 3400
2025-07-20 00:46:55.420 - Creating new thread-specific database connection to data\stats.db for thread 3400
2025-07-20 00:46:55.431 - New database connection created successfully for thread 3400
2025-07-20 00:46:55.432 - Stats database initialized successfully
2025-07-20 00:46:55.436 - ensure_database_exists called from thread 3400
2025-07-20 00:46:55.438 - Using existing connection for thread 3400
2025-07-20 00:46:55.440 - Stats database initialized successfully
2025-07-20 00:46:55.444 - ensure_database_exists called from thread 3400
2025-07-20 00:46:55.446 - Using existing connection for thread 3400
2025-07-20 00:46:55.447 - Stats database initialized successfully
2025-07-20 00:48:48.342 - ensure_database_exists called from thread 2532
2025-07-20 00:48:48.344 - Creating new thread-specific database connection to data\stats.db for thread 2532
2025-07-20 00:48:48.408 - New database connection created successfully for thread 2532
2025-07-20 00:48:48.415 - Stats database initialized successfully
2025-07-20 00:48:48.427 - ensure_database_exists called from thread 2532
2025-07-20 00:48:48.428 - Using existing connection for thread 2532
2025-07-20 00:48:48.429 - Stats database initialized successfully
2025-07-20 00:48:48.452 - ensure_database_exists called from thread 2532
2025-07-20 00:48:48.496 - Using existing connection for thread 2532
2025-07-20 00:48:48.505 - Stats database initialized successfully
2025-07-20 00:50:07.936 - ensure_database_exists called from thread 10200
2025-07-20 00:50:07.938 - Creating new thread-specific database connection to data\stats.db for thread 10200
2025-07-20 00:50:07.947 - New database connection created successfully for thread 10200
2025-07-20 00:50:07.950 - Stats database initialized successfully
2025-07-20 00:50:07.954 - ensure_database_exists called from thread 10200
2025-07-20 00:50:07.956 - Using existing connection for thread 10200
2025-07-20 00:50:07.957 - Stats database initialized successfully
2025-07-20 00:50:07.958 - ensure_database_exists called from thread 10200
2025-07-20 00:50:07.959 - Using existing connection for thread 10200
2025-07-20 00:50:07.960 - Stats database initialized successfully
2025-07-20 01:18:47.934 - ensure_database_exists called from thread 13552
2025-07-20 01:18:47.937 - Creating new thread-specific database connection to data\stats.db for thread 13552
2025-07-20 01:18:47.940 - New database connection created successfully for thread 13552
2025-07-20 01:18:48.002 - Stats database initialized successfully
2025-07-20 01:18:48.004 - ensure_database_exists called from thread 13552
2025-07-20 01:18:48.005 - Using existing connection for thread 13552
2025-07-20 01:18:48.006 - Stats database initialized successfully
2025-07-20 01:18:48.007 - ensure_database_exists called from thread 13552
2025-07-20 01:18:48.008 - Using existing connection for thread 13552
2025-07-20 01:18:48.009 - Stats database initialized successfully
2025-07-20 01:21:28.630 - ensure_database_exists called from thread 9368
2025-07-20 01:21:28.633 - Creating new thread-specific database connection to data\stats.db for thread 9368
2025-07-20 01:21:28.636 - New database connection created successfully for thread 9368
2025-07-20 01:21:28.639 - Stats database initialized successfully
2025-07-20 01:21:28.641 - ensure_database_exists called from thread 9368
2025-07-20 01:21:28.642 - Using existing connection for thread 9368
2025-07-20 01:21:28.643 - Stats database initialized successfully
2025-07-20 01:21:28.644 - ensure_database_exists called from thread 9368
2025-07-20 01:21:28.648 - Using existing connection for thread 9368
2025-07-20 01:21:28.650 - Stats database initialized successfully
2025-07-20 01:27:44.656 - ensure_database_exists called from thread 15872
2025-07-20 01:27:44.658 - Creating new thread-specific database connection to data\stats.db for thread 15872
2025-07-20 01:27:44.661 - New database connection created successfully for thread 15872
2025-07-20 01:27:44.663 - Stats database initialized successfully
2025-07-20 01:27:44.665 - ensure_database_exists called from thread 15872
2025-07-20 01:27:44.666 - Using existing connection for thread 15872
2025-07-20 01:27:44.667 - Stats database initialized successfully
2025-07-20 01:27:44.668 - ensure_database_exists called from thread 15872
2025-07-20 01:27:44.669 - Using existing connection for thread 15872
2025-07-20 01:27:44.670 - Stats database initialized successfully
2025-07-20 01:28:57.465 - ensure_database_exists called from thread 3648
2025-07-20 01:28:57.470 - Creating new thread-specific database connection to data\stats.db for thread 3648
2025-07-20 01:28:57.472 - New database connection created successfully for thread 3648
2025-07-20 01:28:57.476 - Stats database initialized successfully
2025-07-20 01:28:57.478 - ensure_database_exists called from thread 3648
2025-07-20 01:28:57.479 - Using existing connection for thread 3648
2025-07-20 01:28:57.480 - Stats database initialized successfully
2025-07-20 01:29:12.081 - ensure_database_exists called from thread 15976
2025-07-20 01:29:12.083 - Creating new thread-specific database connection to data\stats.db for thread 15976
2025-07-20 01:29:12.085 - New database connection created successfully for thread 15976
2025-07-20 01:29:12.089 - Stats database initialized successfully
2025-07-20 01:29:12.091 - ensure_database_exists called from thread 15976
2025-07-20 01:29:12.094 - Using existing connection for thread 15976
2025-07-20 01:29:12.096 - Stats database initialized successfully
2025-07-20 01:29:12.097 - ensure_database_exists called from thread 15976
2025-07-20 01:29:12.098 - Using existing connection for thread 15976
2025-07-20 01:29:12.099 - Stats database initialized successfully
2025-07-20 01:31:35.090 - ensure_database_exists called from thread 7744
2025-07-20 01:31:35.092 - Creating new thread-specific database connection to data\stats.db for thread 7744
2025-07-20 01:31:35.094 - New database connection created successfully for thread 7744
2025-07-20 01:31:35.097 - Stats database initialized successfully
2025-07-20 01:31:35.098 - ensure_database_exists called from thread 7744
2025-07-20 01:31:35.099 - Using existing connection for thread 7744
2025-07-20 01:31:35.100 - Stats database initialized successfully
2025-07-20 01:31:35.102 - ensure_database_exists called from thread 7744
2025-07-20 01:31:35.103 - Using existing connection for thread 7744
2025-07-20 01:31:35.106 - Stats database initialized successfully
2025-07-20 01:38:15.306 - ensure_database_exists called from thread 16108
2025-07-20 01:38:15.310 - Creating new thread-specific database connection to data\stats.db for thread 16108
2025-07-20 01:38:15.316 - New database connection created successfully for thread 16108
2025-07-20 01:38:15.319 - Stats database initialized successfully
2025-07-20 01:38:15.321 - ensure_database_exists called from thread 16108
2025-07-20 01:38:15.322 - Using existing connection for thread 16108
2025-07-20 01:38:15.323 - Stats database initialized successfully
2025-07-20 01:40:46.246 - ensure_database_exists called from thread 16912
2025-07-20 01:40:46.248 - Creating new thread-specific database connection to data\stats.db for thread 16912
2025-07-20 01:40:46.250 - New database connection created successfully for thread 16912
2025-07-20 01:40:46.254 - Stats database initialized successfully
2025-07-20 01:40:46.256 - ensure_database_exists called from thread 16912
2025-07-20 01:40:46.256 - Using existing connection for thread 16912
2025-07-20 01:40:46.259 - Stats database initialized successfully
2025-07-20 01:40:46.261 - ensure_database_exists called from thread 16912
2025-07-20 01:40:46.261 - Using existing connection for thread 16912
2025-07-20 01:40:46.262 - Stats database initialized successfully
2025-07-20 01:45:42.347 - ensure_database_exists called from thread 14604
2025-07-20 01:45:42.351 - Creating new thread-specific database connection to data\stats.db for thread 14604
2025-07-20 01:45:42.352 - New database connection created successfully for thread 14604
2025-07-20 01:45:42.356 - Stats database initialized successfully
2025-07-20 01:45:42.357 - ensure_database_exists called from thread 14604
2025-07-20 01:45:42.358 - Using existing connection for thread 14604
2025-07-20 01:45:42.359 - Stats database initialized successfully
2025-07-20 01:48:10.595 - ensure_database_exists called from thread 15568
2025-07-20 01:48:10.598 - Creating new thread-specific database connection to data\stats.db for thread 15568
2025-07-20 01:48:10.599 - New database connection created successfully for thread 15568
2025-07-20 01:48:10.605 - Stats database initialized successfully
2025-07-20 01:48:10.607 - ensure_database_exists called from thread 15568
2025-07-20 01:48:10.610 - Using existing connection for thread 15568
2025-07-20 01:48:10.611 - Stats database initialized successfully
2025-07-20 01:48:10.613 - ensure_database_exists called from thread 15568
2025-07-20 01:48:10.615 - Using existing connection for thread 15568
2025-07-20 01:48:10.617 - Stats database initialized successfully
2025-07-20 01:55:28.205 - ensure_database_exists called from thread 14712
2025-07-20 01:55:28.207 - Creating new thread-specific database connection to data\stats.db for thread 14712
2025-07-20 01:55:28.215 - New database connection created successfully for thread 14712
2025-07-20 01:55:28.219 - Stats database initialized successfully
2025-07-20 01:55:28.220 - ensure_database_exists called from thread 14712
2025-07-20 01:55:28.220 - Using existing connection for thread 14712
2025-07-20 01:55:28.221 - Stats database initialized successfully
2025-07-20 01:57:03.542 - ensure_database_exists called from thread 11508
2025-07-20 01:57:03.545 - Creating new thread-specific database connection to data\stats.db for thread 11508
2025-07-20 01:57:03.548 - New database connection created successfully for thread 11508
2025-07-20 01:57:03.552 - Stats database initialized successfully
2025-07-20 01:57:03.554 - ensure_database_exists called from thread 11508
2025-07-20 01:57:03.555 - Using existing connection for thread 11508
2025-07-20 01:57:03.556 - Stats database initialized successfully
2025-07-20 01:57:03.557 - ensure_database_exists called from thread 11508
2025-07-20 01:57:03.558 - Using existing connection for thread 11508
2025-07-20 01:57:03.564 - Stats database initialized successfully
2025-07-20 02:12:11.806 - ensure_database_exists called from thread 3964
2025-07-20 02:12:11.808 - Creating new thread-specific database connection to data\stats.db for thread 3964
2025-07-20 02:12:11.816 - New database connection created successfully for thread 3964
2025-07-20 02:12:11.819 - Stats database initialized successfully
2025-07-20 02:12:11.823 - ensure_database_exists called from thread 3964
2025-07-20 02:12:11.824 - Using existing connection for thread 3964
2025-07-20 02:12:11.825 - Stats database initialized successfully
2025-07-20 02:12:11.828 - ensure_database_exists called from thread 3964
2025-07-20 02:12:11.831 - Using existing connection for thread 3964
2025-07-20 02:12:11.832 - Stats database initialized successfully
2025-07-20 02:29:24.867 - ensure_database_exists called from thread 16368
2025-07-20 02:29:24.868 - Creating new thread-specific database connection to data\stats.db for thread 16368
2025-07-20 02:29:24.876 - New database connection created successfully for thread 16368
2025-07-20 02:29:24.878 - Stats database initialized successfully
2025-07-20 02:29:24.881 - ensure_database_exists called from thread 16368
2025-07-20 02:29:24.883 - Using existing connection for thread 16368
2025-07-20 02:29:24.885 - Stats database initialized successfully
2025-07-20 02:29:24.887 - ensure_database_exists called from thread 16368
2025-07-20 02:29:24.888 - Using existing connection for thread 16368
2025-07-20 02:29:24.889 - Stats database initialized successfully
2025-07-20 02:35:59.768 - ensure_database_exists called from thread 8392
2025-07-20 02:35:59.770 - Creating new thread-specific database connection to data\stats.db for thread 8392
2025-07-20 02:35:59.774 - New database connection created successfully for thread 8392
2025-07-20 02:35:59.775 - Stats database initialized successfully
2025-07-20 02:35:59.776 - ensure_database_exists called from thread 8392
2025-07-20 02:35:59.777 - Using existing connection for thread 8392
2025-07-20 02:35:59.777 - Stats database initialized successfully
2025-07-20 02:36:54.665 - ensure_database_exists called from thread 16020
2025-07-20 02:36:54.668 - Creating new thread-specific database connection to data\stats.db for thread 16020
2025-07-20 02:36:54.672 - New database connection created successfully for thread 16020
2025-07-20 02:36:54.673 - Stats database initialized successfully
2025-07-20 02:36:54.674 - ensure_database_exists called from thread 16020
2025-07-20 02:36:54.675 - Using existing connection for thread 16020
2025-07-20 02:36:54.677 - Stats database initialized successfully
2025-07-20 02:37:51.115 - ensure_database_exists called from thread 984
2025-07-20 02:37:51.119 - Creating new thread-specific database connection to data\stats.db for thread 984
2025-07-20 02:37:51.129 - New database connection created successfully for thread 984
2025-07-20 02:37:51.132 - Stats database initialized successfully
2025-07-20 02:37:51.132 - ensure_database_exists called from thread 984
2025-07-20 02:37:51.133 - Using existing connection for thread 984
2025-07-20 02:37:51.134 - Stats database initialized successfully
2025-07-20 02:40:28.861 - ensure_database_exists called from thread 2888
2025-07-20 02:40:28.863 - Creating new thread-specific database connection to data\stats.db for thread 2888
2025-07-20 02:40:28.876 - New database connection created successfully for thread 2888
2025-07-20 02:40:28.881 - Stats database initialized successfully
2025-07-20 02:40:28.883 - ensure_database_exists called from thread 2888
2025-07-20 02:40:28.884 - Using existing connection for thread 2888
2025-07-20 02:40:28.886 - Stats database initialized successfully
2025-07-20 02:40:28.887 - ensure_database_exists called from thread 2888
2025-07-20 02:40:28.888 - Using existing connection for thread 2888
2025-07-20 02:40:28.889 - Stats database initialized successfully
2025-07-20 02:53:06.683 - ensure_database_exists called from thread 16440
2025-07-20 02:53:06.687 - Creating new thread-specific database connection to data\stats.db for thread 16440
2025-07-20 02:53:06.688 - New database connection created successfully for thread 16440
2025-07-20 02:53:06.690 - Stats database initialized successfully
2025-07-20 02:53:06.691 - ensure_database_exists called from thread 16440
2025-07-20 02:53:06.692 - Using existing connection for thread 16440
2025-07-20 02:53:06.693 - Stats database initialized successfully
2025-07-20 02:53:59.658 - ensure_database_exists called from thread 14876
2025-07-20 02:53:59.664 - Creating new thread-specific database connection to data\stats.db for thread 14876
2025-07-20 02:53:59.666 - New database connection created successfully for thread 14876
2025-07-20 02:53:59.684 - Stats database initialized successfully
2025-07-20 02:53:59.685 - ensure_database_exists called from thread 14876
2025-07-20 02:53:59.686 - Using existing connection for thread 14876
2025-07-20 02:53:59.687 - Stats database initialized successfully
2025-07-20 03:00:17.033 - ensure_database_exists called from thread 17400
2025-07-20 03:00:17.034 - Creating new thread-specific database connection to data\stats.db for thread 17400
2025-07-20 03:00:17.037 - New database connection created successfully for thread 17400
2025-07-20 03:00:17.040 - Stats database initialized successfully
2025-07-20 03:00:17.041 - ensure_database_exists called from thread 17400
2025-07-20 03:00:17.042 - Using existing connection for thread 17400
2025-07-20 03:00:17.043 - Stats database initialized successfully
2025-07-20 03:00:17.044 - ensure_database_exists called from thread 17400
2025-07-20 03:00:17.045 - Using existing connection for thread 17400
2025-07-20 03:00:17.046 - Stats database initialized successfully
2025-07-20 03:01:07.177 - Using existing connection for thread 17400
2025-07-20 03:01:07.251 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-07-20 03:01:07.261 - New database connection created successfully for thread 12412
2025-07-20 03:01:07.467 - Database connection closed for thread 12412
2025-07-20 03:01:07.472 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-07-20 03:01:07.482 - New database connection created successfully for thread 12412
2025-07-20 03:01:07.487 - Database connection closed for thread 12412
2025-07-20 03:01:07.488 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-07-20 03:01:07.490 - New database connection created successfully for thread 12412
2025-07-20 03:01:07.564 - get_summary_stats called from thread 12412
2025-07-20 03:01:07.603 - Using existing connection for thread 12412
2025-07-20 03:01:07.630 - Total earnings from database: 1726.0
2025-07-20 03:01:07.631 - Daily earnings from database: 0.0
2025-07-20 03:01:07.632 - Daily games from database: 0
2025-07-20 03:01:07.648 - Wallet balance from database: 2402.0
2025-07-20 03:01:07.649 - Total games played from database: 65
2025-07-20 03:01:07.650 - Total winners from database: 65
2025-07-20 03:01:07.660 - Returning summary stats: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
