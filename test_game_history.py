#!/usr/bin/env python3
"""
Script to test the game history retrieval functionality
"""
import sys
import os

def test_game_history():
    """Test the game history retrieval"""
    print("Testing game history retrieval...")
    
    try:
        # Import the stats integration module
        from stats_integration import get_game_history
        print("Successfully imported get_game_history")
        
        # Test getting game history
        result = get_game_history(page=0, page_size=10)
        print(f"get_game_history returned: {type(result)}")
        
        if result and isinstance(result, (tuple, list)) and len(result) >= 2:
            history, total_pages = result[0], result[1]
            print(f"History length: {len(history) if history else 0}")
            print(f"Total pages: {total_pages}")
            
            if history:
                print("First few records:")
                for i, record in enumerate(history[:3]):
                    print(f"  Record {i+1}: {record}")
            else:
                print("No history records found")
        else:
            print(f"Unexpected result format: {result}")
            
    except ImportError as e:
        print(f"Import error: {e}")
        
        # Try direct database access
        try:
            from stats_db import get_stats_db_manager
            print("Trying direct database access...")
            
            stats_db = get_stats_db_manager()
            result = stats_db.get_game_history(page=0, page_size=10)
            
            if result and isinstance(result, (tuple, list)) and len(result) >= 2:
                history, total_pages = result[0], result[1]
                print(f"Direct DB - History length: {len(history) if history else 0}")
                print(f"Direct DB - Total pages: {total_pages}")
                
                if history:
                    print("Direct DB - First few records:")
                    for i, record in enumerate(history[:3]):
                        print(f"  Record {i+1}: {record}")
                else:
                    print("Direct DB - No history records found")
            else:
                print(f"Direct DB - Unexpected result format: {result}")
                
        except Exception as db_e:
            print(f"Direct database access error: {db_e}")
            
    except Exception as e:
        print(f"Error testing game history: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_game_history()
