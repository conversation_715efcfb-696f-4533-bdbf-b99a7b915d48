#!/usr/bin/env python3
"""
Check the voucher database structure and content
"""
import sqlite3
import os

def check_voucher_database():
    """Check the voucher database structure"""
    voucher_db_path = 'data/vouchers.db'
    
    if not os.path.exists(voucher_db_path):
        print(f"Voucher database not found at: {voucher_db_path}")
        return
    
    try:
        conn = sqlite3.connect(voucher_db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Tables: {[t[0] for t in tables]}")
        
        # Check each table structure and content
        for table in tables:
            table_name = table[0]
            print(f"\n--- Table: {table_name} ---")
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            schema = cursor.fetchall()
            print("Schema:")
            for col in schema:
                print(f"  {col[1]} {col[2]} {'PRIMARY KEY' if col[5] else ''}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"Row count: {count}")
            
            # Get sample data
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                rows = cursor.fetchall()
                print("Sample data:")
                for row in rows:
                    print(f"  {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error checking voucher database: {e}")
        import traceback
        traceback.print_exc()

def check_usage_tracker_logging():
    """Check if usage tracker is properly logging"""
    print("\n" + "="*50)
    print("CHECKING USAGE TRACKER LOGGING")
    print("="*50)
    
    try:
        # Import the usage tracker
        from payment.usage_tracker import get_usage_tracker
        from payment.voucher_manager import get_voucher_manager
        
        usage_tracker = get_usage_tracker()
        voucher_manager = get_voucher_manager()
        
        print(f"Usage tracker active: {usage_tracker.active}")
        print(f"Current credits: {voucher_manager.credits}")
        
        # Check if log_usage method exists
        if hasattr(voucher_manager, 'log_usage'):
            print("✅ log_usage method exists in voucher_manager")
        else:
            print("❌ log_usage method missing in voucher_manager")
        
        # Check voucher manager database path
        if hasattr(voucher_manager, 'database_path'):
            print(f"Voucher manager database path: {voucher_manager.database_path}")
        
    except Exception as e:
        print(f"Error checking usage tracker: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_voucher_database()
    check_usage_tracker_logging()
