2025-06-13 13:13:29.651 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-13 13:13:29.652 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-13 13:13:29.664 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-13 13:13:29.666 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-06-13 13:13:29.666 - Initializing StatsDataProvider
2025-06-13 13:13:29.668 - Loaded 8 items from cache
2025-06-13 13:13:29.669 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-06-13 13:13:29.670 - Starting background data loading thread
2025-06-13 13:13:29.671 - Background data loading started
2025-06-13 13:13:29.672 - StatsDataProvider initialization completed
2025-06-13 13:13:29.672 - Loading summary statistics
2025-06-13 13:13:29.673 - Created singleton instance of StatsDataProvider on module import
2025-06-13 13:13:29.673 - Attempting to load summary stats from GameStatsIntegration
2025-06-13 13:13:29.673 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-06-13 13:13:29.674 - Data from GameStatsIntegration: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-13 13:13:29.674 - get_stats_provider called, returning provider with initialized=True
2025-06-13 13:13:29.676 - Successfully loaded summary stats from GameStatsIntegration
2025-06-13 13:13:29.677 - Successfully loaded summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-06-13 13:13:29.679 - Successfully loaded weekly stats: 7 days
2025-06-13 13:13:29.681 - Saved 10 items to cache
2025-06-13 13:13:29.681 - Background data loading completed
2025-06-13 13:14:00.680 - get_stats_provider called, returning provider with initialized=True
2025-06-13 13:14:00.694 - get_stats_provider called, returning provider with initialized=True
2025-06-13 13:14:00.697 - get_stats_provider called, returning provider with initialized=True
2025-06-13 13:14:00.697 - Forcing refresh of all stats data
2025-06-13 13:14:00.699 - Attempting to force refresh via GameStatsIntegration
2025-06-13 13:14:00.711 - get_stats_provider called, returning provider with initialized=True
2025-06-13 13:14:00.712 - Force refresh via GameStatsIntegration successful
2025-06-13 13:14:00.713 - Clearing cached data
2025-06-13 13:14:00.714 - Posted refresh_stats event to trigger UI update
2025-06-13 13:14:00.716 - Starting background data reload
2025-06-13 13:14:00.717 - Starting background data loading thread
2025-06-13 13:14:00.718 - Background data loading started
2025-06-13 13:14:00.757 - Loading summary statistics
2025-06-13 13:14:00.758 - Attempting to load summary stats from GameStatsIntegration
2025-06-13 13:14:00.758 - Data from GameStatsIntegration: {'total_earnings': 6.0, 'daily_earnings': 6.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-13 13:14:00.758 - Successfully loaded summary stats from GameStatsIntegration
2025-06-13 13:14:00.759 - Successfully loaded summary stats: {'total_earnings': 6.0, 'daily_earnings': 6.0, 'daily_games': 1, 'wallet_balance': 0}
2025-06-13 13:14:00.759 - Successfully loaded game history: 1 entries
2025-06-13 13:14:00.760 - Successfully loaded weekly stats: 7 days
2025-06-13 13:14:00.761 - Saved 3 items to cache
2025-06-13 13:14:00.761 - Background data loading completed
2025-07-16 17:04:50.521 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 6.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:04:50.523 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 6.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:04:50.560 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 6.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:04:50.560 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-16 17:04:50.562 - Initializing StatsDataProvider
2025-07-16 17:04:50.575 - Loaded 8 items from cache
2025-07-16 17:04:50.575 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-16 17:04:50.576 - Starting background data loading thread
2025-07-16 17:04:50.577 - StatsDataProvider initialization completed
2025-07-16 17:04:50.578 - Loading summary statistics
2025-07-16 17:04:50.578 - Created singleton instance of StatsDataProvider on module import
2025-07-16 17:04:50.578 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:04:50.579 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-16 17:04:50.579 - Data from GameStatsIntegration: {'total_earnings': 6.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:04:50.580 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:04:50.580 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:04:50.582 - Successfully loaded summary stats: {'total_earnings': 6.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:04:50.582 - Successfully loaded game history: 1 entries
2025-07-16 17:04:50.584 - Successfully loaded weekly stats: 7 days
2025-07-16 17:04:50.585 - Saved 10 items to cache
2025-07-16 17:04:50.586 - Background data loading completed
2025-07-16 17:12:39.359 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:12:39.379 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:12:39.381 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:12:39.386 - Forcing refresh of all stats data
2025-07-16 17:12:39.388 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:12:39.403 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:12:39.406 - Force refresh via GameStatsIntegration successful
2025-07-16 17:12:39.414 - Clearing cached data
2025-07-16 17:12:39.416 - Posted refresh_stats event to trigger UI update
2025-07-16 17:12:39.417 - Starting background data reload
2025-07-16 17:12:39.418 - Starting background data loading thread
2025-07-16 17:12:39.419 - Background data loading started
2025-07-16 17:12:39.455 - Loading summary statistics
2025-07-16 17:12:39.455 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:12:39.457 - Data from GameStatsIntegration: {'total_earnings': 18.0, 'daily_earnings': 12.0, 'daily_games': 1, 'wallet_balance': 0}
2025-07-16 17:12:39.458 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:12:39.458 - Successfully loaded summary stats: {'total_earnings': 18.0, 'daily_earnings': 12.0, 'daily_games': 1, 'wallet_balance': 0}
2025-07-16 17:12:39.460 - Successfully loaded game history: 2 entries
2025-07-16 17:12:39.461 - Successfully loaded weekly stats: 7 days
2025-07-16 17:12:39.463 - Saved 3 items to cache
2025-07-16 17:12:39.463 - Background data loading completed
2025-07-16 17:16:04.743 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:19:16.446 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:19:16.470 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:19:16.474 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:19:16.475 - Forcing refresh of all stats data
2025-07-16 17:19:16.478 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:19:16.489 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:19:16.491 - Force refresh via GameStatsIntegration successful
2025-07-16 17:19:16.492 - Clearing cached data
2025-07-16 17:19:16.494 - Posted refresh_stats event to trigger UI update
2025-07-16 17:19:16.496 - Starting background data reload
2025-07-16 17:19:16.497 - Starting background data loading thread
2025-07-16 17:19:16.498 - Background data loading started
2025-07-16 17:19:16.550 - Loading summary statistics
2025-07-16 17:19:16.558 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:19:16.560 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:19:16.563 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:19:16.577 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:19:16.580 - Saved 3 items to cache
2025-07-16 17:19:16.581 - Background data loading completed
2025-07-16 17:21:19.059 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:24:07.154 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:24:07.178 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:24:07.181 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:24:07.182 - Forcing refresh of all stats data
2025-07-16 17:24:07.184 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:24:07.197 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:24:07.198 - Force refresh via GameStatsIntegration successful
2025-07-16 17:24:07.201 - Clearing cached data
2025-07-16 17:24:07.203 - Posted refresh_stats event to trigger UI update
2025-07-16 17:24:07.203 - Starting background data reload
2025-07-16 17:24:07.203 - Starting background data loading thread
2025-07-16 17:24:07.204 - Background data loading started
2025-07-16 17:24:07.221 - Loading summary statistics
2025-07-16 17:24:07.242 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:24:07.243 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:24:07.243 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:24:07.244 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:24:07.248 - Saved 3 items to cache
2025-07-16 17:24:07.250 - Background data loading completed
2025-07-16 17:26:26.943 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:28:41.357 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:28:41.382 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:28:41.386 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:28:41.387 - Forcing refresh of all stats data
2025-07-16 17:28:41.388 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:28:41.414 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:28:41.415 - Force refresh via GameStatsIntegration successful
2025-07-16 17:28:41.417 - Clearing cached data
2025-07-16 17:28:41.420 - Posted refresh_stats event to trigger UI update
2025-07-16 17:28:41.420 - Starting background data reload
2025-07-16 17:28:41.421 - Starting background data loading thread
2025-07-16 17:28:41.422 - Background data loading started
2025-07-16 17:28:41.455 - Loading summary statistics
2025-07-16 17:28:41.464 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:28:41.465 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:28:41.466 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:28:41.467 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:28:41.469 - Saved 3 items to cache
2025-07-16 17:28:41.470 - Background data loading completed
2025-07-16 17:31:12.440 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:33:34.856 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:33:34.871 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:33:34.874 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:33:34.875 - Forcing refresh of all stats data
2025-07-16 17:33:34.879 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:33:34.897 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:33:34.899 - Force refresh via GameStatsIntegration successful
2025-07-16 17:33:34.900 - Clearing cached data
2025-07-16 17:33:34.901 - Posted refresh_stats event to trigger UI update
2025-07-16 17:33:34.902 - Starting background data reload
2025-07-16 17:33:34.904 - Starting background data loading thread
2025-07-16 17:33:34.905 - Background data loading started
2025-07-16 17:33:34.922 - Loading summary statistics
2025-07-16 17:33:34.951 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:33:34.952 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:33:34.953 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:33:34.954 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:33:34.956 - Saved 3 items to cache
2025-07-16 17:33:34.956 - Background data loading completed
2025-07-16 17:35:52.592 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:37:48.660 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:37:48.686 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:37:48.690 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:37:48.692 - Forcing refresh of all stats data
2025-07-16 17:37:48.693 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:37:48.706 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:37:48.707 - Force refresh via GameStatsIntegration successful
2025-07-16 17:37:48.708 - Clearing cached data
2025-07-16 17:37:48.709 - Posted refresh_stats event to trigger UI update
2025-07-16 17:37:48.710 - Starting background data reload
2025-07-16 17:37:48.712 - Starting background data loading thread
2025-07-16 17:37:48.712 - Background data loading started
2025-07-16 17:37:48.748 - Loading summary statistics
2025-07-16 17:37:48.756 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:37:48.757 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:37:48.757 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:37:48.758 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:37:48.761 - Saved 3 items to cache
2025-07-16 17:37:48.761 - Background data loading completed
2025-07-16 17:40:21.243 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:44:02.331 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:44:02.358 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:44:02.362 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:44:02.363 - Forcing refresh of all stats data
2025-07-16 17:44:02.364 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:44:02.377 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:44:02.379 - Force refresh via GameStatsIntegration successful
2025-07-16 17:44:02.380 - Clearing cached data
2025-07-16 17:44:02.380 - Posted refresh_stats event to trigger UI update
2025-07-16 17:44:02.383 - Starting background data reload
2025-07-16 17:44:02.384 - Starting background data loading thread
2025-07-16 17:44:02.388 - Background data loading started
2025-07-16 17:44:02.404 - Loading summary statistics
2025-07-16 17:44:02.419 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:44:02.422 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:44:02.439 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:44:02.468 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:44:02.472 - Saved 3 items to cache
2025-07-16 17:44:02.487 - Background data loading completed
2025-07-16 17:46:24.010 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:50:29.340 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:50:29.353 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:50:29.359 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:50:29.361 - Forcing refresh of all stats data
2025-07-16 17:50:29.362 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:50:29.376 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:50:29.378 - Force refresh via GameStatsIntegration successful
2025-07-16 17:50:29.379 - Clearing cached data
2025-07-16 17:50:29.380 - Posted refresh_stats event to trigger UI update
2025-07-16 17:50:29.384 - Starting background data reload
2025-07-16 17:50:29.385 - Starting background data loading thread
2025-07-16 17:50:29.387 - Background data loading started
2025-07-16 17:50:29.425 - Loading summary statistics
2025-07-16 17:50:29.425 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:50:29.426 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:50:29.427 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:50:29.428 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:50:29.430 - Saved 3 items to cache
2025-07-16 17:50:29.430 - Background data loading completed
2025-07-16 17:55:41.775 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:59:06.375 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:59:06.402 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:59:06.406 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:59:06.408 - Forcing refresh of all stats data
2025-07-16 17:59:06.409 - Attempting to force refresh via GameStatsIntegration
2025-07-16 17:59:06.444 - get_stats_provider called, returning provider with initialized=True
2025-07-16 17:59:06.446 - Force refresh via GameStatsIntegration successful
2025-07-16 17:59:06.447 - Clearing cached data
2025-07-16 17:59:06.449 - Posted refresh_stats event to trigger UI update
2025-07-16 17:59:06.450 - Starting background data reload
2025-07-16 17:59:06.451 - Starting background data loading thread
2025-07-16 17:59:06.452 - Background data loading started
2025-07-16 17:59:06.488 - Loading summary statistics
2025-07-16 17:59:06.489 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 17:59:06.490 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:59:06.490 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 17:59:06.491 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 17:59:06.493 - Saved 3 items to cache
2025-07-16 17:59:06.493 - Background data loading completed
2025-07-16 18:01:38.046 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:06:00.021 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:06:00.039 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:06:00.044 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:06:00.045 - Forcing refresh of all stats data
2025-07-16 18:06:00.049 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:06:00.076 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:06:00.079 - Force refresh via GameStatsIntegration successful
2025-07-16 18:06:00.081 - Clearing cached data
2025-07-16 18:06:00.082 - Posted refresh_stats event to trigger UI update
2025-07-16 18:06:00.083 - Starting background data reload
2025-07-16 18:06:00.085 - Starting background data loading thread
2025-07-16 18:06:00.087 - Background data loading started
2025-07-16 18:06:00.124 - Loading summary statistics
2025-07-16 18:06:00.130 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:06:00.130 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:06:00.131 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:06:00.132 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:06:00.133 - Saved 3 items to cache
2025-07-16 18:06:00.135 - Background data loading completed
2025-07-16 18:08:56.611 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:12:35.966 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:12:35.994 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:12:36.013 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:12:36.014 - Forcing refresh of all stats data
2025-07-16 18:12:36.015 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:12:36.038 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:12:36.040 - Force refresh via GameStatsIntegration successful
2025-07-16 18:12:36.041 - Clearing cached data
2025-07-16 18:12:36.041 - Posted refresh_stats event to trigger UI update
2025-07-16 18:12:36.043 - Starting background data reload
2025-07-16 18:12:36.044 - Starting background data loading thread
2025-07-16 18:12:36.046 - Background data loading started
2025-07-16 18:12:36.087 - Loading summary statistics
2025-07-16 18:12:36.087 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:12:36.088 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:12:36.089 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:12:36.089 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:12:36.090 - Saved 3 items to cache
2025-07-16 18:12:36.091 - Background data loading completed
2025-07-16 18:15:50.430 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:16:52.910 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:16:52.927 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:16:52.934 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:16:52.936 - Forcing refresh of all stats data
2025-07-16 18:16:52.937 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:16:52.966 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:16:52.967 - Force refresh via GameStatsIntegration successful
2025-07-16 18:16:52.969 - Clearing cached data
2025-07-16 18:16:52.970 - Posted refresh_stats event to trigger UI update
2025-07-16 18:16:52.972 - Starting background data reload
2025-07-16 18:16:52.973 - Starting background data loading thread
2025-07-16 18:16:52.974 - Background data loading started
2025-07-16 18:16:53.013 - Loading summary statistics
2025-07-16 18:16:53.018 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:16:53.018 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:16:53.018 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:16:53.019 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:16:53.021 - Saved 3 items to cache
2025-07-16 18:16:53.021 - Background data loading completed
2025-07-16 18:19:56.005 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:21:52.566 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:21:52.592 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:21:52.604 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:21:52.606 - Forcing refresh of all stats data
2025-07-16 18:21:52.607 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:21:52.623 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:21:52.624 - Force refresh via GameStatsIntegration successful
2025-07-16 18:21:52.625 - Clearing cached data
2025-07-16 18:21:52.629 - Posted refresh_stats event to trigger UI update
2025-07-16 18:21:52.631 - Starting background data reload
2025-07-16 18:21:52.631 - Starting background data loading thread
2025-07-16 18:21:52.632 - Background data loading started
2025-07-16 18:21:52.670 - Loading summary statistics
2025-07-16 18:21:52.674 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:21:52.675 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:21:52.675 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:21:52.675 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:21:52.677 - Saved 3 items to cache
2025-07-16 18:21:52.678 - Background data loading completed
2025-07-16 18:25:04.008 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:26:08.463 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:26:08.492 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:26:08.495 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:26:08.498 - Forcing refresh of all stats data
2025-07-16 18:26:08.501 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:26:08.522 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:26:08.523 - Force refresh via GameStatsIntegration successful
2025-07-16 18:26:08.524 - Clearing cached data
2025-07-16 18:26:08.526 - Posted refresh_stats event to trigger UI update
2025-07-16 18:26:08.528 - Starting background data reload
2025-07-16 18:26:08.529 - Starting background data loading thread
2025-07-16 18:26:08.532 - Background data loading started
2025-07-16 18:26:08.560 - Loading summary statistics
2025-07-16 18:26:08.579 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:26:08.579 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:26:08.580 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:26:08.580 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:26:08.582 - Saved 3 items to cache
2025-07-16 18:26:08.582 - Background data loading completed
2025-07-16 18:28:50.767 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:30:57.118 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:30:57.192 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:30:57.196 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:30:57.199 - Forcing refresh of all stats data
2025-07-16 18:30:57.200 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:30:57.214 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:30:57.216 - Force refresh via GameStatsIntegration successful
2025-07-16 18:30:57.217 - Clearing cached data
2025-07-16 18:30:57.218 - Posted refresh_stats event to trigger UI update
2025-07-16 18:30:57.219 - Starting background data reload
2025-07-16 18:30:57.221 - Starting background data loading thread
2025-07-16 18:30:57.224 - Background data loading started
2025-07-16 18:30:57.236 - Loading summary statistics
2025-07-16 18:30:57.238 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:30:57.253 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:30:57.276 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:30:57.278 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:30:57.279 - Saved 3 items to cache
2025-07-16 18:30:57.280 - Background data loading completed
2025-07-16 18:34:48.072 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:36:59.417 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:36:59.438 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:36:59.441 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:36:59.443 - Forcing refresh of all stats data
2025-07-16 18:36:59.443 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:36:59.457 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:36:59.458 - Force refresh via GameStatsIntegration successful
2025-07-16 18:36:59.459 - Clearing cached data
2025-07-16 18:36:59.464 - Posted refresh_stats event to trigger UI update
2025-07-16 18:36:59.465 - Starting background data reload
2025-07-16 18:36:59.466 - Starting background data loading thread
2025-07-16 18:36:59.467 - Background data loading started
2025-07-16 18:36:59.505 - Loading summary statistics
2025-07-16 18:36:59.507 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:36:59.509 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:36:59.510 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:36:59.511 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:36:59.512 - Saved 3 items to cache
2025-07-16 18:36:59.513 - Background data loading completed
2025-07-16 18:40:16.318 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:42:06.210 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:42:06.368 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:42:06.430 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:42:06.434 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:42:06.434 - Forcing refresh of all stats data
2025-07-16 18:42:06.437 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:42:06.517 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:42:06.524 - Force refresh via GameStatsIntegration successful
2025-07-16 18:42:06.532 - Clearing cached data
2025-07-16 18:42:06.543 - Posted refresh_stats event to trigger UI update
2025-07-16 18:42:06.548 - Starting background data reload
2025-07-16 18:42:06.556 - Starting background data loading thread
2025-07-16 18:42:06.564 - Background data loading started
2025-07-16 18:42:06.567 - Loading summary statistics
2025-07-16 18:42:06.611 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:42:06.623 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:42:06.628 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:42:06.633 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:42:06.650 - Saved 3 items to cache
2025-07-16 18:42:06.656 - Background data loading completed
2025-07-16 18:43:33.983 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:43:34.007 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:43:34.009 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:43:34.011 - Forcing refresh of all stats data
2025-07-16 18:43:34.012 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:43:34.051 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:43:34.052 - Force refresh via GameStatsIntegration successful
2025-07-16 18:43:34.054 - Clearing cached data
2025-07-16 18:43:34.054 - Posted refresh_stats event to trigger UI update
2025-07-16 18:43:34.055 - Starting background data reload
2025-07-16 18:43:34.056 - Starting background data loading thread
2025-07-16 18:43:34.060 - Background data loading started
2025-07-16 18:43:34.105 - Loading summary statistics
2025-07-16 18:43:34.106 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:43:34.109 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:43:34.109 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:43:34.109 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:43:34.126 - Saved 3 items to cache
2025-07-16 18:43:34.127 - Background data loading completed
2025-07-16 18:46:47.779 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:51:06.110 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:51:06.139 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:51:06.141 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:51:06.144 - Forcing refresh of all stats data
2025-07-16 18:51:06.145 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:51:06.156 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:51:06.159 - Force refresh via GameStatsIntegration successful
2025-07-16 18:51:06.160 - Clearing cached data
2025-07-16 18:51:06.162 - Posted refresh_stats event to trigger UI update
2025-07-16 18:51:06.163 - Starting background data reload
2025-07-16 18:51:06.165 - Starting background data loading thread
2025-07-16 18:51:06.167 - Background data loading started
2025-07-16 18:51:06.207 - Loading summary statistics
2025-07-16 18:51:06.208 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:51:06.209 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:51:06.210 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:51:06.212 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:51:06.213 - Saved 3 items to cache
2025-07-16 18:51:06.214 - Background data loading completed
2025-07-16 18:54:50.466 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:57:36.044 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:57:36.069 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:57:36.073 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:57:36.075 - Forcing refresh of all stats data
2025-07-16 18:57:36.079 - Attempting to force refresh via GameStatsIntegration
2025-07-16 18:57:36.092 - get_stats_provider called, returning provider with initialized=True
2025-07-16 18:57:36.094 - Force refresh via GameStatsIntegration successful
2025-07-16 18:57:36.096 - Clearing cached data
2025-07-16 18:57:36.097 - Posted refresh_stats event to trigger UI update
2025-07-16 18:57:36.098 - Starting background data reload
2025-07-16 18:57:36.100 - Starting background data loading thread
2025-07-16 18:57:36.102 - Background data loading started
2025-07-16 18:57:36.143 - Loading summary statistics
2025-07-16 18:57:36.143 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 18:57:36.145 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:57:36.146 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 18:57:36.147 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 18:57:36.148 - Saved 3 items to cache
2025-07-16 18:57:36.149 - Background data loading completed
2025-07-16 18:59:44.437 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:01:21.190 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:01:21.226 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:01:21.229 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:01:21.232 - Forcing refresh of all stats data
2025-07-16 19:01:21.234 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:01:21.254 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:01:21.256 - Force refresh via GameStatsIntegration successful
2025-07-16 19:01:21.258 - Clearing cached data
2025-07-16 19:01:21.260 - Posted refresh_stats event to trigger UI update
2025-07-16 19:01:21.262 - Starting background data reload
2025-07-16 19:01:21.263 - Starting background data loading thread
2025-07-16 19:01:21.265 - Background data loading started
2025-07-16 19:01:21.274 - Loading summary statistics
2025-07-16 19:01:21.274 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:01:21.276 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:01:21.277 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:01:21.278 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:01:21.316 - Saved 3 items to cache
2025-07-16 19:01:21.317 - Background data loading completed
2025-07-16 19:05:12.636 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:06:47.949 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:06:47.964 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:06:47.966 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:06:47.967 - Forcing refresh of all stats data
2025-07-16 19:06:47.970 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:06:47.998 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:06:48.000 - Force refresh via GameStatsIntegration successful
2025-07-16 19:06:48.002 - Clearing cached data
2025-07-16 19:06:48.003 - Posted refresh_stats event to trigger UI update
2025-07-16 19:06:48.004 - Starting background data reload
2025-07-16 19:06:48.005 - Starting background data loading thread
2025-07-16 19:06:48.008 - Background data loading started
2025-07-16 19:06:48.047 - Loading summary statistics
2025-07-16 19:06:48.054 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:06:48.054 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:06:48.054 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:06:48.055 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:06:48.057 - Saved 3 items to cache
2025-07-16 19:06:48.057 - Background data loading completed
2025-07-16 19:10:34.071 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:12:43.776 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:12:43.791 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:12:43.794 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:12:43.795 - Forcing refresh of all stats data
2025-07-16 19:12:43.796 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:12:43.827 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:12:43.831 - Force refresh via GameStatsIntegration successful
2025-07-16 19:12:43.832 - Clearing cached data
2025-07-16 19:12:43.835 - Posted refresh_stats event to trigger UI update
2025-07-16 19:12:43.837 - Starting background data reload
2025-07-16 19:12:43.837 - Starting background data loading thread
2025-07-16 19:12:43.839 - Background data loading started
2025-07-16 19:12:43.877 - Loading summary statistics
2025-07-16 19:12:43.879 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:12:43.879 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:12:43.880 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:12:43.880 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:12:43.882 - Saved 3 items to cache
2025-07-16 19:12:43.883 - Background data loading completed
2025-07-16 19:15:20.406 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:18:20.520 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:18:20.535 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:18:20.539 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:18:20.540 - Forcing refresh of all stats data
2025-07-16 19:18:20.542 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:18:20.556 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:18:20.557 - Force refresh via GameStatsIntegration successful
2025-07-16 19:18:20.558 - Clearing cached data
2025-07-16 19:18:20.559 - Posted refresh_stats event to trigger UI update
2025-07-16 19:18:20.561 - Starting background data reload
2025-07-16 19:18:20.563 - Starting background data loading thread
2025-07-16 19:18:20.564 - Background data loading started
2025-07-16 19:18:20.606 - Loading summary statistics
2025-07-16 19:18:20.607 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:18:20.609 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:18:20.609 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:18:20.610 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:18:20.612 - Saved 3 items to cache
2025-07-16 19:18:20.613 - Background data loading completed
2025-07-16 19:21:26.305 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:22:57.284 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:22:57.311 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:22:57.314 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:22:57.316 - Forcing refresh of all stats data
2025-07-16 19:22:57.319 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:22:57.342 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:22:57.345 - Force refresh via GameStatsIntegration successful
2025-07-16 19:22:57.346 - Clearing cached data
2025-07-16 19:22:57.348 - Posted refresh_stats event to trigger UI update
2025-07-16 19:22:57.353 - Starting background data reload
2025-07-16 19:22:57.354 - Starting background data loading thread
2025-07-16 19:22:57.358 - Background data loading started
2025-07-16 19:22:57.399 - Loading summary statistics
2025-07-16 19:22:57.399 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:22:57.400 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:22:57.401 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:22:57.401 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:22:57.402 - Saved 3 items to cache
2025-07-16 19:22:57.403 - Background data loading completed
2025-07-16 19:25:47.710 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:27:59.451 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:27:59.472 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:27:59.475 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:27:59.477 - Forcing refresh of all stats data
2025-07-16 19:27:59.479 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:27:59.492 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:27:59.493 - Force refresh via GameStatsIntegration successful
2025-07-16 19:27:59.495 - Clearing cached data
2025-07-16 19:27:59.496 - Posted refresh_stats event to trigger UI update
2025-07-16 19:27:59.497 - Starting background data reload
2025-07-16 19:27:59.499 - Starting background data loading thread
2025-07-16 19:27:59.500 - Background data loading started
2025-07-16 19:27:59.522 - Loading summary statistics
2025-07-16 19:27:59.543 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:27:59.544 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:27:59.545 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:27:59.547 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:27:59.548 - Saved 3 items to cache
2025-07-16 19:27:59.549 - Background data loading completed
2025-07-16 19:30:42.799 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:32:39.672 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:32:39.702 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:32:39.707 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:32:39.708 - Forcing refresh of all stats data
2025-07-16 19:32:39.711 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:32:39.734 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:32:39.735 - Force refresh via GameStatsIntegration successful
2025-07-16 19:32:39.736 - Clearing cached data
2025-07-16 19:32:39.737 - Posted refresh_stats event to trigger UI update
2025-07-16 19:32:39.739 - Starting background data reload
2025-07-16 19:32:39.740 - Starting background data loading thread
2025-07-16 19:32:39.742 - Background data loading started
2025-07-16 19:32:39.784 - Loading summary statistics
2025-07-16 19:32:39.785 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:32:39.786 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:32:39.786 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:32:39.788 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:32:39.801 - Saved 3 items to cache
2025-07-16 19:32:39.802 - Background data loading completed
2025-07-16 19:34:22.840 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:37:18.321 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:37:18.350 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:37:18.352 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:37:18.359 - Forcing refresh of all stats data
2025-07-16 19:37:18.360 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:37:18.387 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:37:18.388 - Force refresh via GameStatsIntegration successful
2025-07-16 19:37:18.390 - Clearing cached data
2025-07-16 19:37:18.390 - Posted refresh_stats event to trigger UI update
2025-07-16 19:37:18.391 - Starting background data reload
2025-07-16 19:37:18.393 - Starting background data loading thread
2025-07-16 19:37:18.395 - Background data loading started
2025-07-16 19:37:18.435 - Loading summary statistics
2025-07-16 19:37:18.435 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:37:18.436 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:37:18.436 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:37:18.437 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:37:18.438 - Saved 3 items to cache
2025-07-16 19:37:18.439 - Background data loading completed
2025-07-16 19:39:14.925 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:40:37.752 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:40:37.818 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:40:37.822 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:40:37.823 - Forcing refresh of all stats data
2025-07-16 19:40:37.824 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:40:37.847 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:40:37.849 - Force refresh via GameStatsIntegration successful
2025-07-16 19:40:37.850 - Clearing cached data
2025-07-16 19:40:37.851 - Posted refresh_stats event to trigger UI update
2025-07-16 19:40:37.852 - Starting background data reload
2025-07-16 19:40:37.854 - Starting background data loading thread
2025-07-16 19:40:37.857 - Background data loading started
2025-07-16 19:40:37.902 - Loading summary statistics
2025-07-16 19:40:37.903 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:40:37.904 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:40:37.904 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:40:37.905 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:40:37.907 - Saved 3 items to cache
2025-07-16 19:40:37.907 - Background data loading completed
2025-07-16 19:43:14.663 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:46:22.172 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:46:22.205 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:46:22.217 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:46:22.219 - Forcing refresh of all stats data
2025-07-16 19:46:22.220 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:46:22.231 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:46:22.233 - Force refresh via GameStatsIntegration successful
2025-07-16 19:46:22.235 - Clearing cached data
2025-07-16 19:46:22.236 - Posted refresh_stats event to trigger UI update
2025-07-16 19:46:22.238 - Starting background data reload
2025-07-16 19:46:22.239 - Starting background data loading thread
2025-07-16 19:46:22.240 - Background data loading started
2025-07-16 19:46:22.280 - Loading summary statistics
2025-07-16 19:46:22.280 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:46:22.281 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:46:22.282 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:46:22.282 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:46:22.283 - Saved 3 items to cache
2025-07-16 19:46:22.283 - Background data loading completed
2025-07-16 19:49:35.749 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:52:11.218 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:52:11.242 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:52:11.253 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:52:11.254 - Forcing refresh of all stats data
2025-07-16 19:52:11.256 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:52:11.278 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:52:11.280 - Force refresh via GameStatsIntegration successful
2025-07-16 19:52:11.280 - Clearing cached data
2025-07-16 19:52:11.281 - Posted refresh_stats event to trigger UI update
2025-07-16 19:52:11.282 - Starting background data reload
2025-07-16 19:52:11.283 - Starting background data loading thread
2025-07-16 19:52:11.287 - Background data loading started
2025-07-16 19:52:11.328 - Loading summary statistics
2025-07-16 19:52:11.329 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:52:11.330 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:52:11.331 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:52:11.343 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 19:52:11.349 - Saved 3 items to cache
2025-07-16 19:52:11.353 - Background data loading completed
2025-07-16 19:54:49.953 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1338.0, 'daily_earnings': 1332.0, 'daily_games': 31, 'wallet_balance': 0}
2025-07-16 19:54:49.955 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1338.0, 'daily_earnings': 1332.0, 'daily_games': 31, 'wallet_balance': 0}
2025-07-16 19:54:49.973 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1338.0, 'daily_earnings': 1332.0, 'daily_games': 31, 'wallet_balance': 0}
2025-07-16 19:54:49.973 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-16 19:54:49.974 - Initializing StatsDataProvider
2025-07-16 19:54:49.997 - Loaded 8 items from cache
2025-07-16 19:54:49.998 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-16 19:54:49.999 - Starting background data loading thread
2025-07-16 19:54:50.001 - Background data loading started
2025-07-16 19:54:50.002 - StatsDataProvider initialization completed
2025-07-16 19:54:50.002 - Loading summary statistics
2025-07-16 19:54:50.003 - Created singleton instance of StatsDataProvider on module import
2025-07-16 19:54:50.003 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:54:50.003 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-16 19:54:50.005 - Data from GameStatsIntegration: {'total_earnings': 1338.0, 'daily_earnings': 1332.0, 'daily_games': 31, 'wallet_balance': 0}
2025-07-16 19:54:50.005 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:54:50.007 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:54:50.008 - Successfully loaded summary stats: {'total_earnings': 1338.0, 'daily_earnings': 1332.0, 'daily_games': 31, 'wallet_balance': 0}
2025-07-16 19:54:50.010 - Successfully loaded game history: 10 entries
2025-07-16 19:54:50.012 - Successfully loaded weekly stats: 7 days
2025-07-16 19:54:50.015 - Saved 10 items to cache
2025-07-16 19:54:50.015 - Background data loading completed
2025-07-16 19:56:48.058 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:56:48.104 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:56:48.108 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:56:48.110 - Forcing refresh of all stats data
2025-07-16 19:56:48.113 - Attempting to force refresh via GameStatsIntegration
2025-07-16 19:56:48.128 - get_stats_provider called, returning provider with initialized=True
2025-07-16 19:56:48.129 - Force refresh via GameStatsIntegration successful
2025-07-16 19:56:48.130 - Clearing cached data
2025-07-16 19:56:48.131 - Posted refresh_stats event to trigger UI update
2025-07-16 19:56:48.133 - Starting background data reload
2025-07-16 19:56:48.134 - Starting background data loading thread
2025-07-16 19:56:48.136 - Background data loading started
2025-07-16 19:56:48.163 - Loading summary statistics
2025-07-16 19:56:48.164 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 19:56:48.179 - Data from GameStatsIntegration: {'total_earnings': 1378.0, 'daily_earnings': 1372.0, 'daily_games': 32, 'wallet_balance': 0}
2025-07-16 19:56:48.184 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 19:56:48.187 - Successfully loaded summary stats: {'total_earnings': 1378.0, 'daily_earnings': 1372.0, 'daily_games': 32, 'wallet_balance': 0}
2025-07-16 19:56:48.192 - Successfully loaded game history: 10 entries
2025-07-16 19:56:48.194 - Successfully loaded weekly stats: 7 days
2025-07-16 19:56:48.200 - Saved 3 items to cache
2025-07-16 19:56:48.200 - Background data loading completed
2025-07-16 19:58:56.621 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:00:19.993 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:00:20.031 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:00:20.035 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:00:20.036 - Forcing refresh of all stats data
2025-07-16 20:00:20.037 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:00:20.050 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:00:20.051 - Force refresh via GameStatsIntegration successful
2025-07-16 20:00:20.051 - Clearing cached data
2025-07-16 20:00:20.054 - Posted refresh_stats event to trigger UI update
2025-07-16 20:00:20.054 - Starting background data reload
2025-07-16 20:00:20.055 - Starting background data loading thread
2025-07-16 20:00:20.057 - Background data loading started
2025-07-16 20:00:20.081 - Loading summary statistics
2025-07-16 20:00:20.086 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:00:20.087 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:00:20.088 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:00:20.089 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:00:20.092 - Saved 3 items to cache
2025-07-16 20:00:20.092 - Background data loading completed
2025-07-16 20:02:09.441 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:05:47.338 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:05:47.353 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:05:47.357 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:05:47.359 - Forcing refresh of all stats data
2025-07-16 20:05:47.361 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:05:47.386 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:05:47.387 - Force refresh via GameStatsIntegration successful
2025-07-16 20:05:47.388 - Clearing cached data
2025-07-16 20:05:47.391 - Posted refresh_stats event to trigger UI update
2025-07-16 20:05:47.392 - Starting background data reload
2025-07-16 20:05:47.393 - Starting background data loading thread
2025-07-16 20:05:47.397 - Background data loading started
2025-07-16 20:05:47.426 - Loading summary statistics
2025-07-16 20:05:47.427 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:05:47.429 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:05:47.430 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:05:47.430 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:05:47.431 - Saved 3 items to cache
2025-07-16 20:05:47.433 - Background data loading completed
2025-07-16 20:08:03.299 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:09:48.390 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:09:48.422 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:09:48.426 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:09:48.427 - Forcing refresh of all stats data
2025-07-16 20:09:48.428 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:09:48.451 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:09:48.453 - Force refresh via GameStatsIntegration successful
2025-07-16 20:09:48.455 - Clearing cached data
2025-07-16 20:09:48.456 - Posted refresh_stats event to trigger UI update
2025-07-16 20:09:48.457 - Starting background data reload
2025-07-16 20:09:48.458 - Starting background data loading thread
2025-07-16 20:09:48.461 - Background data loading started
2025-07-16 20:09:48.492 - Loading summary statistics
2025-07-16 20:09:48.494 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:09:48.495 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:09:48.495 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:09:48.495 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:09:48.498 - Saved 3 items to cache
2025-07-16 20:09:48.498 - Background data loading completed
2025-07-16 20:13:17.247 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:16:31.905 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:16:31.935 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:16:31.939 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:16:31.939 - Forcing refresh of all stats data
2025-07-16 20:16:31.940 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:16:31.962 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:16:31.964 - Force refresh via GameStatsIntegration successful
2025-07-16 20:16:31.964 - Clearing cached data
2025-07-16 20:16:31.966 - Posted refresh_stats event to trigger UI update
2025-07-16 20:16:31.968 - Starting background data reload
2025-07-16 20:16:31.969 - Starting background data loading thread
2025-07-16 20:16:31.972 - Background data loading started
2025-07-16 20:16:31.999 - Loading summary statistics
2025-07-16 20:16:31.999 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:16:32.000 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:16:32.000 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:16:32.001 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:16:32.026 - Saved 3 items to cache
2025-07-16 20:16:32.027 - Background data loading completed
2025-07-16 20:20:06.839 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:23:56.201 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:23:56.220 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:23:56.224 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:23:56.225 - Forcing refresh of all stats data
2025-07-16 20:23:56.226 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:23:56.258 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:23:56.261 - Force refresh via GameStatsIntegration successful
2025-07-16 20:23:56.266 - Clearing cached data
2025-07-16 20:23:56.270 - Posted refresh_stats event to trigger UI update
2025-07-16 20:23:56.271 - Starting background data reload
2025-07-16 20:23:56.271 - Starting background data loading thread
2025-07-16 20:23:56.278 - Background data loading started
2025-07-16 20:23:56.297 - Loading summary statistics
2025-07-16 20:23:56.308 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:23:56.309 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:23:56.309 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:23:56.309 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:23:56.311 - Saved 3 items to cache
2025-07-16 20:23:56.311 - Background data loading completed
2025-07-16 20:28:19.451 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:30:37.025 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:30:37.053 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:30:37.056 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:30:37.057 - Forcing refresh of all stats data
2025-07-16 20:30:37.058 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:30:37.077 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:30:37.078 - Force refresh via GameStatsIntegration successful
2025-07-16 20:30:37.079 - Clearing cached data
2025-07-16 20:30:37.080 - Posted refresh_stats event to trigger UI update
2025-07-16 20:30:37.080 - Starting background data reload
2025-07-16 20:30:37.082 - Starting background data loading thread
2025-07-16 20:30:37.083 - Background data loading started
2025-07-16 20:30:37.111 - Loading summary statistics
2025-07-16 20:30:37.112 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:30:37.114 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:30:37.115 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:30:37.116 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:30:37.132 - Saved 3 items to cache
2025-07-16 20:30:37.134 - Background data loading completed
2025-07-16 20:32:48.144 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:35:10.273 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:35:10.296 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:35:10.303 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:35:10.305 - Forcing refresh of all stats data
2025-07-16 20:35:10.308 - Attempting to force refresh via GameStatsIntegration
2025-07-16 20:35:10.335 - get_stats_provider called, returning provider with initialized=True
2025-07-16 20:35:10.337 - Force refresh via GameStatsIntegration successful
2025-07-16 20:35:10.338 - Clearing cached data
2025-07-16 20:35:10.339 - Posted refresh_stats event to trigger UI update
2025-07-16 20:35:10.340 - Starting background data reload
2025-07-16 20:35:10.341 - Starting background data loading thread
2025-07-16 20:35:10.344 - Background data loading started
2025-07-16 20:35:10.373 - Loading summary statistics
2025-07-16 20:35:10.373 - Attempting to load summary stats from GameStatsIntegration
2025-07-16 20:35:10.374 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:35:10.374 - Successfully loaded summary stats from GameStatsIntegration
2025-07-16 20:35:10.376 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-16 20:35:10.379 - Saved 3 items to cache
2025-07-16 20:35:10.379 - Background data loading completed
2025-07-17 12:58:25.320 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1678.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 12:58:25.359 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1678.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 12:58:25.377 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1678.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 12:58:25.378 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-17 12:58:25.379 - Initializing StatsDataProvider
2025-07-17 12:58:25.380 - Loaded 8 items from cache
2025-07-17 12:58:25.380 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-17 12:58:25.381 - Starting background data loading thread
2025-07-17 12:58:25.381 - Background data loading started
2025-07-17 12:58:25.381 - StatsDataProvider initialization completed
2025-07-17 12:58:25.382 - Created singleton instance of StatsDataProvider on module import
2025-07-17 12:58:25.382 - Loading summary statistics
2025-07-17 12:58:25.383 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-17 12:58:25.383 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 12:58:25.384 - Data from GameStatsIntegration: {'total_earnings': 1678.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 12:58:25.384 - get_stats_provider called, returning provider with initialized=True
2025-07-17 12:58:25.385 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 12:58:25.386 - Successfully loaded summary stats: {'total_earnings': 1678.0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 12:58:25.388 - Successfully loaded game history: 10 entries
2025-07-17 12:58:25.389 - Successfully loaded weekly stats: 7 days
2025-07-17 12:58:25.393 - Saved 10 items to cache
2025-07-17 12:58:25.395 - Background data loading completed
2025-07-17 13:06:24.316 - get_stats_provider called, returning provider with initialized=True
2025-07-17 13:11:04.652 - get_stats_provider called, returning provider with initialized=True
2025-07-17 13:18:42.628 - get_stats_provider called, returning provider with initialized=True
2025-07-17 13:33:39.620 - get_stats_provider called, returning provider with initialized=True
2025-07-17 13:39:55.805 - get_stats_provider called, returning provider with initialized=True
2025-07-17 13:49:22.395 - get_stats_provider called, returning provider with initialized=True
2025-07-17 13:57:25.319 - get_stats_provider called, returning provider with initialized=True
2025-07-17 14:08:38.163 - get_stats_provider called, returning provider with initialized=True
2025-07-17 14:16:45.588 - get_stats_provider called, returning provider with initialized=True
2025-07-17 14:43:43.005 - get_stats_provider called, returning provider with initialized=True
2025-07-17 14:50:14.479 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:09:05.677 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:16:38.325 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:22:57.807 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:30:04.353 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:39:12.375 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:43:51.714 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:51:17.613 - get_stats_provider called, returning provider with initialized=True
2025-07-17 15:57:28.660 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:07:23.359 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:15:51.824 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:24:26.411 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:33:04.203 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:34:58.898 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:34:59.004 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:34:59.028 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:34:59.033 - Forcing refresh of all stats data
2025-07-17 16:34:59.041 - Attempting to force refresh via GameStatsIntegration
2025-07-17 16:34:59.153 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:34:59.155 - Force refresh via GameStatsIntegration successful
2025-07-17 16:34:59.156 - Clearing cached data
2025-07-17 16:34:59.184 - Posted refresh_stats event to trigger UI update
2025-07-17 16:34:59.190 - Starting background data reload
2025-07-17 16:34:59.203 - Starting background data loading thread
2025-07-17 16:34:59.214 - Background data loading started
2025-07-17 16:34:59.258 - Loading summary statistics
2025-07-17 16:34:59.265 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 16:34:59.273 - Data from GameStatsIntegration: {'total_earnings': 1702.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-07-17 16:34:59.277 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 16:34:59.280 - Successfully loaded summary stats: {'total_earnings': 1702.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-07-17 16:34:59.283 - Successfully loaded game history: 10 entries
2025-07-17 16:34:59.284 - Successfully loaded weekly stats: 7 days
2025-07-17 16:34:59.291 - Saved 3 items to cache
2025-07-17 16:34:59.313 - Background data loading completed
2025-07-17 16:36:59.579 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:39:00.980 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:39:01.008 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:39:01.016 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:39:01.017 - Forcing refresh of all stats data
2025-07-17 16:39:01.018 - Attempting to force refresh via GameStatsIntegration
2025-07-17 16:39:01.047 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:39:01.049 - Force refresh via GameStatsIntegration successful
2025-07-17 16:39:01.050 - Clearing cached data
2025-07-17 16:39:01.052 - Posted refresh_stats event to trigger UI update
2025-07-17 16:39:01.057 - Starting background data reload
2025-07-17 16:39:01.059 - Starting background data loading thread
2025-07-17 16:39:01.062 - Background data loading started
2025-07-17 16:39:01.089 - Loading summary statistics
2025-07-17 16:39:01.090 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 16:39:01.092 - Data from GameStatsIntegration: {'total_earnings': 1726.0, 'daily_earnings': 48.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-17 16:39:01.092 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 16:39:01.093 - Successfully loaded summary stats: {'total_earnings': 1726.0, 'daily_earnings': 48.0, 'daily_games': 2, 'wallet_balance': 0}
2025-07-17 16:39:01.094 - Successfully loaded game history: 10 entries
2025-07-17 16:39:01.094 - Successfully loaded weekly stats: 7 days
2025-07-17 16:39:01.097 - Saved 3 items to cache
2025-07-17 16:39:01.097 - Background data loading completed
2025-07-17 16:40:44.947 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:43:14.711 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:43:14.727 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:43:14.730 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:43:14.731 - Forcing refresh of all stats data
2025-07-17 16:43:14.732 - Attempting to force refresh via GameStatsIntegration
2025-07-17 16:43:14.756 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:43:14.757 - Force refresh via GameStatsIntegration successful
2025-07-17 16:43:14.759 - Clearing cached data
2025-07-17 16:43:14.760 - Posted refresh_stats event to trigger UI update
2025-07-17 16:43:14.762 - Starting background data reload
2025-07-17 16:43:14.763 - Starting background data loading thread
2025-07-17 16:43:14.766 - Background data loading started
2025-07-17 16:43:14.798 - Loading summary statistics
2025-07-17 16:43:14.799 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 16:43:14.800 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:43:14.800 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 16:43:14.801 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:43:14.805 - Saved 3 items to cache
2025-07-17 16:43:14.805 - Background data loading completed
2025-07-17 16:45:41.225 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:47:44.384 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:47:44.403 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:47:44.412 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:47:44.416 - Forcing refresh of all stats data
2025-07-17 16:47:44.419 - Attempting to force refresh via GameStatsIntegration
2025-07-17 16:47:44.431 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:47:44.433 - Force refresh via GameStatsIntegration successful
2025-07-17 16:47:44.434 - Clearing cached data
2025-07-17 16:47:44.435 - Posted refresh_stats event to trigger UI update
2025-07-17 16:47:44.435 - Starting background data reload
2025-07-17 16:47:44.437 - Starting background data loading thread
2025-07-17 16:47:44.441 - Background data loading started
2025-07-17 16:47:44.465 - Loading summary statistics
2025-07-17 16:47:44.465 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 16:47:44.466 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:47:44.467 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 16:47:44.467 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:47:44.478 - Saved 3 items to cache
2025-07-17 16:47:44.478 - Background data loading completed
2025-07-17 16:49:15.447 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:51:45.139 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:51:45.158 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:51:45.161 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:51:45.162 - Forcing refresh of all stats data
2025-07-17 16:51:45.165 - Attempting to force refresh via GameStatsIntegration
2025-07-17 16:51:45.187 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:51:45.188 - Force refresh via GameStatsIntegration successful
2025-07-17 16:51:45.190 - Clearing cached data
2025-07-17 16:51:45.191 - Posted refresh_stats event to trigger UI update
2025-07-17 16:51:45.192 - Starting background data reload
2025-07-17 16:51:45.193 - Starting background data loading thread
2025-07-17 16:51:45.196 - Background data loading started
2025-07-17 16:51:45.222 - Loading summary statistics
2025-07-17 16:51:45.223 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 16:51:45.223 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:51:45.224 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 16:51:45.224 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:51:45.225 - Saved 3 items to cache
2025-07-17 16:51:45.226 - Background data loading completed
2025-07-17 16:54:02.529 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:57:30.829 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:57:30.863 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:57:30.866 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:57:30.867 - Forcing refresh of all stats data
2025-07-17 16:57:30.868 - Attempting to force refresh via GameStatsIntegration
2025-07-17 16:57:30.896 - get_stats_provider called, returning provider with initialized=True
2025-07-17 16:57:30.896 - Force refresh via GameStatsIntegration successful
2025-07-17 16:57:30.898 - Clearing cached data
2025-07-17 16:57:30.898 - Posted refresh_stats event to trigger UI update
2025-07-17 16:57:30.899 - Starting background data reload
2025-07-17 16:57:30.901 - Starting background data loading thread
2025-07-17 16:57:30.902 - Background data loading started
2025-07-17 16:57:30.927 - Loading summary statistics
2025-07-17 16:57:30.930 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 16:57:30.937 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:57:30.939 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 16:57:30.941 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 16:57:30.976 - Saved 3 items to cache
2025-07-17 16:57:30.977 - Background data loading completed
2025-07-17 17:00:15.484 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:02:45.005 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:02:45.029 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:02:45.035 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:02:45.036 - Forcing refresh of all stats data
2025-07-17 17:02:45.038 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:02:45.064 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:02:45.066 - Force refresh via GameStatsIntegration successful
2025-07-17 17:02:45.067 - Clearing cached data
2025-07-17 17:02:45.071 - Posted refresh_stats event to trigger UI update
2025-07-17 17:02:45.072 - Starting background data reload
2025-07-17 17:02:45.074 - Starting background data loading thread
2025-07-17 17:02:45.080 - Background data loading started
2025-07-17 17:02:45.108 - Loading summary statistics
2025-07-17 17:02:45.109 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:02:45.110 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:02:45.111 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:02:45.112 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:02:45.114 - Saved 3 items to cache
2025-07-17 17:02:45.114 - Background data loading completed
2025-07-17 17:05:17.113 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:08:14.081 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:08:14.098 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:08:14.100 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:08:14.101 - Forcing refresh of all stats data
2025-07-17 17:08:14.103 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:08:14.128 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:08:14.129 - Force refresh via GameStatsIntegration successful
2025-07-17 17:08:14.130 - Clearing cached data
2025-07-17 17:08:14.132 - Posted refresh_stats event to trigger UI update
2025-07-17 17:08:14.134 - Starting background data reload
2025-07-17 17:08:14.136 - Starting background data loading thread
2025-07-17 17:08:14.139 - Background data loading started
2025-07-17 17:08:14.168 - Loading summary statistics
2025-07-17 17:08:14.174 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:08:14.175 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:08:14.176 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:08:14.177 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:08:14.179 - Saved 3 items to cache
2025-07-17 17:08:14.179 - Background data loading completed
2025-07-17 17:10:23.999 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:12:23.330 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:12:23.361 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:12:23.364 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:12:23.366 - Forcing refresh of all stats data
2025-07-17 17:12:23.368 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:12:23.381 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:12:23.382 - Force refresh via GameStatsIntegration successful
2025-07-17 17:12:23.383 - Clearing cached data
2025-07-17 17:12:23.385 - Posted refresh_stats event to trigger UI update
2025-07-17 17:12:23.385 - Starting background data reload
2025-07-17 17:12:23.386 - Starting background data loading thread
2025-07-17 17:12:23.390 - Background data loading started
2025-07-17 17:12:23.414 - Loading summary statistics
2025-07-17 17:12:23.415 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:12:23.415 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:12:23.416 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:12:23.418 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:12:23.420 - Saved 3 items to cache
2025-07-17 17:12:23.421 - Background data loading completed
2025-07-17 17:14:28.238 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:15:37.079 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:15:37.101 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:15:37.105 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:15:37.107 - Forcing refresh of all stats data
2025-07-17 17:15:37.110 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:15:37.129 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:15:37.130 - Force refresh via GameStatsIntegration successful
2025-07-17 17:15:37.131 - Clearing cached data
2025-07-17 17:15:37.139 - Posted refresh_stats event to trigger UI update
2025-07-17 17:15:37.139 - Starting background data reload
2025-07-17 17:15:37.140 - Starting background data loading thread
2025-07-17 17:15:37.142 - Background data loading started
2025-07-17 17:15:37.170 - Loading summary statistics
2025-07-17 17:15:37.170 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:15:37.171 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:15:37.171 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:15:37.172 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:15:37.173 - Saved 3 items to cache
2025-07-17 17:15:37.175 - Background data loading completed
2025-07-17 17:16:59.479 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:19:23.689 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:19:23.716 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:19:23.719 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:19:23.720 - Forcing refresh of all stats data
2025-07-17 17:19:23.723 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:19:23.737 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:19:23.738 - Force refresh via GameStatsIntegration successful
2025-07-17 17:19:23.739 - Clearing cached data
2025-07-17 17:19:23.741 - Posted refresh_stats event to trigger UI update
2025-07-17 17:19:23.742 - Starting background data reload
2025-07-17 17:19:23.757 - Starting background data loading thread
2025-07-17 17:19:23.771 - Background data loading started
2025-07-17 17:19:23.773 - Loading summary statistics
2025-07-17 17:19:23.777 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:19:23.806 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:19:23.820 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:19:23.822 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:19:23.839 - Saved 3 items to cache
2025-07-17 17:19:23.839 - Background data loading completed
2025-07-17 17:21:59.815 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:23:12.632 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:23:12.664 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:23:12.669 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:23:12.672 - Forcing refresh of all stats data
2025-07-17 17:23:12.675 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:23:12.688 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:23:12.689 - Force refresh via GameStatsIntegration successful
2025-07-17 17:23:12.691 - Clearing cached data
2025-07-17 17:23:12.692 - Posted refresh_stats event to trigger UI update
2025-07-17 17:23:12.692 - Starting background data reload
2025-07-17 17:23:12.694 - Starting background data loading thread
2025-07-17 17:23:12.697 - Background data loading started
2025-07-17 17:23:12.725 - Loading summary statistics
2025-07-17 17:23:12.725 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:23:12.726 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:23:12.727 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:23:12.728 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:23:12.730 - Saved 3 items to cache
2025-07-17 17:23:12.731 - Background data loading completed
2025-07-17 17:26:01.239 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:28:07.822 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:28:07.854 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:28:07.859 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:28:07.860 - Forcing refresh of all stats data
2025-07-17 17:28:07.862 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:28:07.878 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:28:07.880 - Force refresh via GameStatsIntegration successful
2025-07-17 17:28:07.882 - Clearing cached data
2025-07-17 17:28:07.882 - Posted refresh_stats event to trigger UI update
2025-07-17 17:28:07.884 - Starting background data reload
2025-07-17 17:28:07.885 - Starting background data loading thread
2025-07-17 17:28:07.888 - Background data loading started
2025-07-17 17:28:07.917 - Loading summary statistics
2025-07-17 17:28:07.918 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:28:07.919 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:28:07.919 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:28:07.920 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:28:07.921 - Saved 3 items to cache
2025-07-17 17:28:07.922 - Background data loading completed
2025-07-17 17:29:58.851 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:32:51.642 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:32:51.657 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:32:51.661 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:32:51.661 - Forcing refresh of all stats data
2025-07-17 17:32:51.662 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:32:51.678 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:32:51.680 - Force refresh via GameStatsIntegration successful
2025-07-17 17:32:51.682 - Clearing cached data
2025-07-17 17:32:51.685 - Posted refresh_stats event to trigger UI update
2025-07-17 17:32:51.687 - Starting background data reload
2025-07-17 17:32:51.689 - Starting background data loading thread
2025-07-17 17:32:51.691 - Background data loading started
2025-07-17 17:32:51.722 - Loading summary statistics
2025-07-17 17:32:51.723 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:32:51.723 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:32:51.725 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:32:51.725 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:32:51.727 - Saved 3 items to cache
2025-07-17 17:32:51.727 - Background data loading completed
2025-07-17 17:34:31.910 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 2090.0, 'daily_earnings': 412.0, 'daily_games': 14, 'wallet_balance': 0}
2025-07-17 17:34:31.919 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 2090.0, 'daily_earnings': 412.0, 'daily_games': 14, 'wallet_balance': 0}
2025-07-17 17:34:31.942 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 2090.0, 'daily_earnings': 412.0, 'daily_games': 14, 'wallet_balance': 0}
2025-07-17 17:34:31.944 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-17 17:34:31.945 - Initializing StatsDataProvider
2025-07-17 17:34:31.960 - Loaded 8 items from cache
2025-07-17 17:34:31.961 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-17 17:34:31.962 - Starting background data loading thread
2025-07-17 17:34:31.964 - StatsDataProvider initialization completed
2025-07-17 17:34:31.965 - Loading summary statistics
2025-07-17 17:34:31.965 - Created singleton instance of StatsDataProvider on module import
2025-07-17 17:34:31.966 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:34:31.966 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-17 17:34:31.967 - Data from GameStatsIntegration: {'total_earnings': 2090.0, 'daily_earnings': 412.0, 'daily_games': 14, 'wallet_balance': 0}
2025-07-17 17:34:31.968 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:34:31.969 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:34:31.970 - Successfully loaded summary stats: {'total_earnings': 2090.0, 'daily_earnings': 412.0, 'daily_games': 14, 'wallet_balance': 0}
2025-07-17 17:34:31.974 - Successfully loaded game history: 10 entries
2025-07-17 17:34:31.980 - Successfully loaded weekly stats: 7 days
2025-07-17 17:34:31.988 - Saved 10 items to cache
2025-07-17 17:34:31.989 - Background data loading completed
2025-07-17 17:35:38.155 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:35:38.172 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:35:38.175 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:35:38.175 - Forcing refresh of all stats data
2025-07-17 17:35:38.176 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:35:38.207 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:35:38.212 - Force refresh via GameStatsIntegration successful
2025-07-17 17:35:38.213 - Clearing cached data
2025-07-17 17:35:38.214 - Posted refresh_stats event to trigger UI update
2025-07-17 17:35:38.215 - Starting background data reload
2025-07-17 17:35:38.215 - Starting background data loading thread
2025-07-17 17:35:38.221 - Background data loading started
2025-07-17 17:35:38.250 - Loading summary statistics
2025-07-17 17:35:38.251 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:35:38.252 - Data from GameStatsIntegration: {'total_earnings': 2118.0, 'daily_earnings': 440.0, 'daily_games': 15, 'wallet_balance': 0}
2025-07-17 17:35:38.253 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:35:38.253 - Successfully loaded summary stats: {'total_earnings': 2118.0, 'daily_earnings': 440.0, 'daily_games': 15, 'wallet_balance': 0}
2025-07-17 17:35:38.254 - Successfully loaded game history: 10 entries
2025-07-17 17:35:38.255 - Successfully loaded weekly stats: 7 days
2025-07-17 17:35:38.270 - Saved 3 items to cache
2025-07-17 17:35:38.270 - Background data loading completed
2025-07-17 17:37:45.433 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:39:07.319 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:39:07.347 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:39:07.350 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:39:07.351 - Forcing refresh of all stats data
2025-07-17 17:39:07.353 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:39:07.384 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:39:07.385 - Force refresh via GameStatsIntegration successful
2025-07-17 17:39:07.392 - Clearing cached data
2025-07-17 17:39:07.393 - Posted refresh_stats event to trigger UI update
2025-07-17 17:39:07.393 - Starting background data reload
2025-07-17 17:39:07.394 - Starting background data loading thread
2025-07-17 17:39:07.395 - Background data loading started
2025-07-17 17:39:07.423 - Loading summary statistics
2025-07-17 17:39:07.424 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:39:07.424 - Data from GameStatsIntegration: {'total_earnings': 2146.0, 'daily_earnings': 468.0, 'daily_games': 16, 'wallet_balance': 0}
2025-07-17 17:39:07.425 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:39:07.427 - Successfully loaded summary stats: {'total_earnings': 2146.0, 'daily_earnings': 468.0, 'daily_games': 16, 'wallet_balance': 0}
2025-07-17 17:39:07.428 - Successfully loaded game history: 10 entries
2025-07-17 17:39:07.429 - Successfully loaded weekly stats: 7 days
2025-07-17 17:39:07.433 - Saved 3 items to cache
2025-07-17 17:39:07.434 - Background data loading completed
2025-07-17 17:41:12.185 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:42:53.302 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:42:53.332 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:42:53.340 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:42:53.341 - Forcing refresh of all stats data
2025-07-17 17:42:53.342 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:42:53.417 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:42:53.419 - Force refresh via GameStatsIntegration successful
2025-07-17 17:42:53.420 - Clearing cached data
2025-07-17 17:42:53.421 - Posted refresh_stats event to trigger UI update
2025-07-17 17:42:53.422 - Starting background data reload
2025-07-17 17:42:53.434 - Starting background data loading thread
2025-07-17 17:42:53.447 - Background data loading started
2025-07-17 17:42:53.482 - Loading summary statistics
2025-07-17 17:42:53.483 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:42:53.485 - Data from GameStatsIntegration: {'total_earnings': 2170.0, 'daily_earnings': 492.0, 'daily_games': 17, 'wallet_balance': 0}
2025-07-17 17:42:53.489 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:42:53.497 - Successfully loaded summary stats: {'total_earnings': 2170.0, 'daily_earnings': 492.0, 'daily_games': 17, 'wallet_balance': 0}
2025-07-17 17:42:53.510 - Successfully loaded game history: 10 entries
2025-07-17 17:42:53.514 - Successfully loaded weekly stats: 7 days
2025-07-17 17:42:53.518 - Saved 3 items to cache
2025-07-17 17:42:53.522 - Background data loading completed
2025-07-17 17:44:46.350 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:47:29.500 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:47:29.520 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:47:29.523 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:47:29.525 - Forcing refresh of all stats data
2025-07-17 17:47:29.526 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:47:29.561 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:47:29.562 - Force refresh via GameStatsIntegration successful
2025-07-17 17:47:29.572 - Clearing cached data
2025-07-17 17:47:29.573 - Posted refresh_stats event to trigger UI update
2025-07-17 17:47:29.575 - Starting background data reload
2025-07-17 17:47:29.576 - Starting background data loading thread
2025-07-17 17:47:29.578 - Background data loading started
2025-07-17 17:47:29.613 - Loading summary statistics
2025-07-17 17:47:29.613 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:47:29.614 - Data from GameStatsIntegration: {'total_earnings': 2186.0, 'daily_earnings': 508.0, 'daily_games': 18, 'wallet_balance': 0}
2025-07-17 17:47:29.615 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:47:29.615 - Successfully loaded summary stats: {'total_earnings': 2186.0, 'daily_earnings': 508.0, 'daily_games': 18, 'wallet_balance': 0}
2025-07-17 17:47:29.616 - Successfully loaded game history: 10 entries
2025-07-17 17:47:29.617 - Successfully loaded weekly stats: 7 days
2025-07-17 17:47:29.620 - Saved 3 items to cache
2025-07-17 17:47:29.621 - Background data loading completed
2025-07-17 17:49:07.279 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:50:45.621 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:50:45.644 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:50:45.650 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:50:45.652 - Forcing refresh of all stats data
2025-07-17 17:50:45.653 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:50:45.686 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:50:45.687 - Force refresh via GameStatsIntegration successful
2025-07-17 17:50:45.688 - Clearing cached data
2025-07-17 17:50:45.690 - Posted refresh_stats event to trigger UI update
2025-07-17 17:50:45.691 - Starting background data reload
2025-07-17 17:50:45.692 - Starting background data loading thread
2025-07-17 17:50:45.693 - Background data loading started
2025-07-17 17:50:45.719 - Loading summary statistics
2025-07-17 17:50:45.719 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:50:45.720 - Data from GameStatsIntegration: {'total_earnings': 2206.0, 'daily_earnings': 528.0, 'daily_games': 19, 'wallet_balance': 0}
2025-07-17 17:50:45.721 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:50:45.721 - Successfully loaded summary stats: {'total_earnings': 2206.0, 'daily_earnings': 528.0, 'daily_games': 19, 'wallet_balance': 0}
2025-07-17 17:50:45.722 - Successfully loaded game history: 10 entries
2025-07-17 17:50:45.723 - Successfully loaded weekly stats: 7 days
2025-07-17 17:50:45.755 - Saved 3 items to cache
2025-07-17 17:50:45.757 - Background data loading completed
2025-07-17 17:52:18.603 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:53:38.815 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:53:38.850 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:53:38.855 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:53:38.857 - Forcing refresh of all stats data
2025-07-17 17:53:38.858 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:53:38.871 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:53:38.872 - Force refresh via GameStatsIntegration successful
2025-07-17 17:53:38.873 - Clearing cached data
2025-07-17 17:53:38.874 - Posted refresh_stats event to trigger UI update
2025-07-17 17:53:38.875 - Starting background data reload
2025-07-17 17:53:38.876 - Starting background data loading thread
2025-07-17 17:53:38.877 - Background data loading started
2025-07-17 17:53:38.901 - Loading summary statistics
2025-07-17 17:53:38.913 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:53:38.954 - Data from GameStatsIntegration: {'total_earnings': 2230.0, 'daily_earnings': 552.0, 'daily_games': 20, 'wallet_balance': 0}
2025-07-17 17:53:38.955 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:53:38.955 - Successfully loaded summary stats: {'total_earnings': 2230.0, 'daily_earnings': 552.0, 'daily_games': 20, 'wallet_balance': 0}
2025-07-17 17:53:38.960 - Successfully loaded game history: 10 entries
2025-07-17 17:53:38.973 - Successfully loaded weekly stats: 7 days
2025-07-17 17:53:38.978 - Saved 3 items to cache
2025-07-17 17:53:38.981 - Background data loading completed
2025-07-17 17:56:13.060 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:58:39.090 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:58:39.139 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:58:39.143 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:58:39.145 - Forcing refresh of all stats data
2025-07-17 17:58:39.148 - Attempting to force refresh via GameStatsIntegration
2025-07-17 17:58:39.158 - get_stats_provider called, returning provider with initialized=True
2025-07-17 17:58:39.160 - Force refresh via GameStatsIntegration successful
2025-07-17 17:58:39.161 - Clearing cached data
2025-07-17 17:58:39.162 - Posted refresh_stats event to trigger UI update
2025-07-17 17:58:39.162 - Starting background data reload
2025-07-17 17:58:39.163 - Starting background data loading thread
2025-07-17 17:58:39.166 - Background data loading started
2025-07-17 17:58:39.198 - Loading summary statistics
2025-07-17 17:58:39.199 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 17:58:39.200 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:58:39.201 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 17:58:39.203 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 17:58:39.205 - Saved 3 items to cache
2025-07-17 17:58:39.206 - Background data loading completed
2025-07-17 18:01:05.287 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:02:43.592 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:02:43.623 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:02:43.626 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:02:43.630 - Forcing refresh of all stats data
2025-07-17 18:02:43.631 - Attempting to force refresh via GameStatsIntegration
2025-07-17 18:02:43.654 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:02:43.655 - Force refresh via GameStatsIntegration successful
2025-07-17 18:02:43.655 - Clearing cached data
2025-07-17 18:02:43.657 - Posted refresh_stats event to trigger UI update
2025-07-17 18:02:43.658 - Starting background data reload
2025-07-17 18:02:43.658 - Starting background data loading thread
2025-07-17 18:02:43.661 - Background data loading started
2025-07-17 18:02:43.684 - Loading summary statistics
2025-07-17 18:02:43.685 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 18:02:43.686 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:02:43.687 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 18:02:43.688 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:02:43.697 - Saved 3 items to cache
2025-07-17 18:02:43.699 - Background data loading completed
2025-07-17 18:05:56.658 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:08:16.384 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:08:16.402 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:08:16.407 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:08:16.408 - Forcing refresh of all stats data
2025-07-17 18:08:16.409 - Attempting to force refresh via GameStatsIntegration
2025-07-17 18:08:16.422 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:08:16.424 - Force refresh via GameStatsIntegration successful
2025-07-17 18:08:16.425 - Clearing cached data
2025-07-17 18:08:16.426 - Posted refresh_stats event to trigger UI update
2025-07-17 18:08:16.426 - Starting background data reload
2025-07-17 18:08:16.427 - Starting background data loading thread
2025-07-17 18:08:16.431 - Background data loading started
2025-07-17 18:08:16.447 - Loading summary statistics
2025-07-17 18:08:16.463 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 18:08:16.467 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:08:16.470 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 18:08:16.472 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:08:16.479 - Saved 3 items to cache
2025-07-17 18:08:16.481 - Background data loading completed
2025-07-17 18:09:47.902 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:12:35.783 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:12:35.800 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:12:35.803 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:12:35.804 - Forcing refresh of all stats data
2025-07-17 18:12:35.804 - Attempting to force refresh via GameStatsIntegration
2025-07-17 18:12:35.831 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:12:35.832 - Force refresh via GameStatsIntegration successful
2025-07-17 18:12:35.834 - Clearing cached data
2025-07-17 18:12:35.835 - Posted refresh_stats event to trigger UI update
2025-07-17 18:12:35.835 - Starting background data reload
2025-07-17 18:12:35.838 - Starting background data loading thread
2025-07-17 18:12:35.841 - Background data loading started
2025-07-17 18:12:35.852 - Loading summary statistics
2025-07-17 18:12:35.855 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 18:12:35.876 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:12:35.894 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 18:12:35.896 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:12:35.910 - Saved 3 items to cache
2025-07-17 18:12:35.913 - Background data loading completed
2025-07-17 18:14:31.590 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:16:15.589 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:16:15.616 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:16:15.619 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:16:15.620 - Forcing refresh of all stats data
2025-07-17 18:16:15.622 - Attempting to force refresh via GameStatsIntegration
2025-07-17 18:16:15.659 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:16:15.661 - Force refresh via GameStatsIntegration successful
2025-07-17 18:16:15.662 - Clearing cached data
2025-07-17 18:16:15.663 - Posted refresh_stats event to trigger UI update
2025-07-17 18:16:15.664 - Starting background data reload
2025-07-17 18:16:15.665 - Starting background data loading thread
2025-07-17 18:16:15.666 - Background data loading started
2025-07-17 18:16:15.680 - Loading summary statistics
2025-07-17 18:16:15.696 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 18:16:15.700 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:16:15.701 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 18:16:15.701 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-07-17 18:16:15.703 - Saved 3 items to cache
2025-07-17 18:16:15.703 - Background data loading completed
2025-07-17 18:26:14.210 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 18:26:14.211 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 18:26:14.217 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 18:26:14.217 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-17 18:26:14.218 - Initializing StatsDataProvider
2025-07-17 18:26:14.234 - Loaded 8 items from cache
2025-07-17 18:26:14.237 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-17 18:26:14.238 - Starting background data loading thread
2025-07-17 18:26:14.239 - Background data loading started
2025-07-17 18:26:14.239 - StatsDataProvider initialization completed
2025-07-17 18:26:14.241 - Loading summary statistics
2025-07-17 18:26:14.241 - Created singleton instance of StatsDataProvider on module import
2025-07-17 18:26:14.242 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 18:26:14.243 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-17 18:26:14.243 - Data from GameStatsIntegration: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 18:26:14.244 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:26:14.245 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 18:26:14.246 - Successfully loaded summary stats: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 18:26:14.248 - Successfully loaded game history: 10 entries
2025-07-17 18:26:14.252 - Successfully loaded weekly stats: 7 days
2025-07-17 18:26:14.256 - Saved 10 items to cache
2025-07-17 18:26:14.257 - Background data loading completed
2025-07-17 18:27:36.691 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:28:16.583 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:30:24.936 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:31:52.077 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:34:15.391 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:35:06.189 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:39:35.419 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:40:58.884 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:43:11.162 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:44:26.272 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:47:37.997 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:48:49.540 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:51:23.929 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:52:47.677 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:56:06.506 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:57:51.051 - get_stats_provider called, returning provider with initialized=True
2025-07-17 18:59:55.651 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:01:32.565 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:03:53.560 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:04:49.031 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:09:13.835 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:10:17.352 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:11:49.852 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:13:27.266 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:16:45.738 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:18:13.894 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:20:57.636 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:23:00.277 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:28:15.224 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:28:53.662 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:31:36.133 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:33:51.256 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:36:41.662 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:37:20.317 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:41:40.027 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:42:56.038 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:44:42.672 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:46:24.162 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:49:34.873 - get_stats_provider called, returning provider with initialized=True
2025-07-17 19:50:14.329 - get_stats_provider called, returning provider with initialized=True
2025-07-17 20:04:13.641 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 20:04:13.645 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 20:04:13.654 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 20:04:13.654 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-17 20:04:13.655 - Initializing StatsDataProvider
2025-07-17 20:04:13.676 - Loaded 8 items from cache
2025-07-17 20:04:13.678 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-17 20:04:13.680 - Starting background data loading thread
2025-07-17 20:04:13.684 - Background data loading started
2025-07-17 20:04:13.684 - StatsDataProvider initialization completed
2025-07-17 20:04:13.685 - Loading summary statistics
2025-07-17 20:04:13.685 - Created singleton instance of StatsDataProvider on module import
2025-07-17 20:04:13.686 - Attempting to load summary stats from GameStatsIntegration
2025-07-17 20:04:13.687 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-17 20:04:13.687 - Data from GameStatsIntegration: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 20:04:13.688 - get_stats_provider called, returning provider with initialized=True
2025-07-17 20:04:13.688 - Successfully loaded summary stats from GameStatsIntegration
2025-07-17 20:04:13.689 - Successfully loaded summary stats: {'total_earnings': 2402.0, 'daily_earnings': 724.0, 'daily_games': 25, 'wallet_balance': 0}
2025-07-17 20:04:13.694 - Successfully loaded game history: 10 entries
2025-07-17 20:04:13.696 - Successfully loaded weekly stats: 7 days
2025-07-17 20:04:13.700 - Saved 10 items to cache
2025-07-17 20:04:13.701 - Background data loading completed
2025-07-17 20:05:44.901 - get_stats_provider called, returning provider with initialized=True
2025-07-17 20:07:55.287 - get_stats_provider called, returning provider with initialized=True
2025-07-18 04:05:50.859 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-18 04:05:50.864 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-18 04:05:50.903 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-18 04:05:50.912 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-18 04:05:50.914 - Initializing StatsDataProvider
2025-07-18 04:05:50.934 - Loaded 8 items from cache
2025-07-18 04:05:50.935 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-18 04:05:50.935 - Starting background data loading thread
2025-07-18 04:05:50.937 - Background data loading started
2025-07-18 04:05:50.937 - StatsDataProvider initialization completed
2025-07-18 04:05:50.938 - Created singleton instance of StatsDataProvider on module import
2025-07-18 04:05:50.938 - Loading summary statistics
2025-07-18 04:05:50.939 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-18 04:05:50.940 - get_stats_provider called, returning provider with initialized=True
2025-07-18 04:05:50.940 - Data from GameStatsIntegration: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-18 04:05:50.942 - Successfully loaded summary stats from GameStatsIntegration
2025-07-18 04:05:50.946 - Successfully loaded summary stats: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-18 04:05:50.947 - Successfully loaded game history: 10 entries
2025-07-18 04:05:50.955 - Successfully loaded weekly stats: 7 days
2025-07-18 04:05:50.968 - Saved 10 items to cache
2025-07-18 04:05:50.969 - Background data loading completed
2025-07-20 03:01:07.508 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-20 03:01:07.546 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-20 03:01:07.664 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-20 03:01:07.679 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-07-20 03:01:07.699 - Initializing StatsDataProvider
2025-07-20 03:01:07.747 - Loaded 8 items from cache
2025-07-20 03:01:07.750 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-07-20 03:01:07.754 - Starting background data loading thread
2025-07-20 03:01:07.757 - Background data loading started
2025-07-20 03:01:07.757 - StatsDataProvider initialization completed
2025-07-20 03:01:07.767 - Loading summary statistics
2025-07-20 03:01:07.768 - Created singleton instance of StatsDataProvider on module import
2025-07-20 03:01:07.769 - Attempting to load summary stats from GameStatsIntegration
2025-07-20 03:01:07.769 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-07-20 03:01:07.770 - Data from GameStatsIntegration: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-20 03:01:07.770 - get_stats_provider called, returning provider with initialized=True
2025-07-20 03:01:07.771 - Successfully loaded summary stats from GameStatsIntegration
2025-07-20 03:01:07.772 - Successfully loaded summary stats: {'total_earnings': 1726.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 2402.0}
2025-07-20 03:01:07.773 - Successfully loaded game history: 10 entries
2025-07-20 03:01:07.774 - Successfully loaded weekly stats: 7 days
2025-07-20 03:01:07.778 - Saved 10 items to cache
2025-07-20 03:01:07.779 - Background data loading completed
