#!/usr/bin/env python3
"""
Script to test the stats fixes
"""
import sys
import os

def test_filtered_game_history():
    """Test the filtered game history retrieval"""
    print("Testing filtered game history retrieval...")
    
    try:
        # Import the stats integration module
        from stats_integration import get_game_history
        print("Successfully imported get_game_history")
        
        # Test getting game history with new filtering
        result = get_game_history(page=0, page_size=20)
        print(f"get_game_history returned: {type(result)}")
        
        if result and isinstance(result, (tuple, list)) and len(result) >= 2:
            history, total_pages = result[0], result[1]
            print(f"Filtered history length: {len(history) if history else 0}")
            print(f"Total pages: {total_pages}")
            
            if history:
                print("Filtered records:")
                for i, record in enumerate(history):
                    username = record.get('username', 'Unknown')
                    total_calls = record.get('total_calls', 0)
                    players = record.get('players', 0)
                    status = record.get('status', 'Unknown')
                    print(f"  {i+1}. {username} - {total_calls} calls - {players} players - {status}")
                    
                # Check if any Game Reset entries with 0 calls remain
                reset_entries = [r for r in history if r.get('username') == 'Game Reset' and r.get('total_calls', 0) == 0]
                if reset_entries:
                    print(f"WARNING: {len(reset_entries)} meaningless Game Reset entries still present!")
                else:
                    print("✅ No meaningless Game Reset entries found - filtering working!")
                    
                # Check for demo entries
                demo_entries = [r for r in history if 'Demo' in r.get('username', '')]
                if demo_entries:
                    print(f"WARNING: {len(demo_entries)} demo entries still present!")
                else:
                    print("✅ No demo entries found - filtering working!")
                    
                # Check for entries with 0 calls
                zero_call_entries = [r for r in history if r.get('total_calls', 0) == 0]
                if zero_call_entries:
                    print(f"WARNING: {len(zero_call_entries)} entries with 0 calls still present!")
                else:
                    print("✅ No entries with 0 calls found - filtering working!")
                    
            else:
                print("No filtered history records found")
        else:
            print(f"Unexpected result format: {result}")
            
    except Exception as e:
        print(f"Error testing filtered game history: {e}")
        import traceback
        traceback.print_exc()

def test_direct_database_filtering():
    """Test the direct database filtering"""
    print("\nTesting direct database filtering...")
    
    try:
        from stats_db import get_stats_db_manager
        print("Testing direct database access with filtering...")
        
        stats_db = get_stats_db_manager()
        result = stats_db.get_game_history(page=0, page_size=20)
        
        if result and isinstance(result, (tuple, list)) and len(result) >= 2:
            history, total_pages = result[0], result[1]
            print(f"Direct DB filtered history length: {len(history) if history else 0}")
            print(f"Direct DB total pages: {total_pages}")
            
            if history:
                print("Direct DB filtered records:")
                for i, record in enumerate(history[:5]):  # Show first 5
                    username = record.get('username', 'Unknown')
                    total_calls = record.get('total_calls', 0)
                    players = record.get('players', 0)
                    print(f"  {i+1}. {username} - {total_calls} calls - {players} players")
            else:
                print("No direct DB filtered records found")
        else:
            print(f"Direct DB unexpected result format: {result}")
            
    except Exception as e:
        print(f"Error testing direct database filtering: {e}")
        import traceback
        traceback.print_exc()

def compare_before_after():
    """Compare raw database vs filtered results"""
    print("\nComparing raw database vs filtered results...")
    
    try:
        import sqlite3
        
        # Get raw count
        conn = sqlite3.connect('data/stats.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM game_history')
        raw_total = cursor.fetchone()[0]
        
        # Get filtered count
        cursor.execute('''
        SELECT COUNT(*) FROM game_history
        WHERE (
            total_calls > 0
            AND username NOT LIKE '%Demo%'
            AND NOT (username = 'Game Reset' AND total_calls = 0)
            AND players > 0
        )
        ''')
        filtered_total = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"Raw database records: {raw_total}")
        print(f"Filtered records: {filtered_total}")
        print(f"Filtered out: {raw_total - filtered_total} records ({((raw_total - filtered_total) / raw_total * 100):.1f}%)")
        
        if filtered_total > 0:
            print("✅ Filtering is working - meaningful games are being shown")
        else:
            print("⚠️ No meaningful games found - may need to adjust filtering criteria")
            
    except Exception as e:
        print(f"Error comparing before/after: {e}")

if __name__ == '__main__':
    test_filtered_game_history()
    test_direct_database_filtering()
    compare_before_after()
