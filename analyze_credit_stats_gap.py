#!/usr/bin/env python3
"""
Analyze the gap between credit deductions and stats recording
"""
import sqlite3
import os

def analyze_credit_deductions():
    """Analyze credit deduction records"""
    print("="*60)
    print("ANALYZING CREDIT DEDUCTIONS")
    print("="*60)
    
    # Check if voucher database exists
    voucher_db_path = 'data/vouchers.db'
    if not os.path.exists(voucher_db_path):
        print(f"Voucher database not found at: {voucher_db_path}")
        return
    
    try:
        conn = sqlite3.connect(voucher_db_path)
        cursor = conn.cursor()
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Voucher DB tables: {[t[0] for t in tables]}")
        
        # Check usage_log table
        if any('usage_log' in t for t in tables):
            cursor.execute('SELECT COUNT(*) FROM usage_log')
            usage_count = cursor.fetchone()[0]
            print(f"Total usage log entries: {usage_count}")
            
            if usage_count > 0:
                cursor.execute('''
                SELECT id, credits_used, share_percentage, game_id, timestamp, total_bets, commission_percentage
                FROM usage_log 
                ORDER BY timestamp DESC 
                LIMIT 10
                ''')
                recent_usage = cursor.fetchall()
                print("\nRecent credit deductions:")
                for usage in recent_usage:
                    print(f"  ID:{usage[0]} - {usage[1]} credits - Game:{usage[3]} - Bets:{usage[5]} ETB")
        
        conn.close()
        
    except Exception as e:
        print(f"Error analyzing voucher database: {e}")

def analyze_stats_records():
    """Analyze stats database records"""
    print("\n" + "="*60)
    print("ANALYZING STATS RECORDS")
    print("="*60)
    
    stats_db_path = 'data/stats.db'
    if not os.path.exists(stats_db_path):
        print(f"Stats database not found at: {stats_db_path}")
        return
    
    try:
        conn = sqlite3.connect(stats_db_path)
        cursor = conn.cursor()
        
        # Get total count
        cursor.execute('SELECT COUNT(*) FROM game_history')
        total_count = cursor.fetchone()[0]
        print(f"Total game history records: {total_count}")
        
        # Get meaningful games count (using our filtering)
        cursor.execute('''
        SELECT COUNT(*) FROM game_history
        WHERE (
            total_calls > 0
            AND username NOT LIKE '%Demo%'
            AND NOT (username = 'Game Reset' AND total_calls = 0)
            AND players > 0
        )
        ''')
        meaningful_count = cursor.fetchone()[0]
        print(f"Meaningful game records: {meaningful_count}")
        
        # Get recent meaningful games
        cursor.execute('''
        SELECT id, date_time, username, players, total_calls, stake, total_prize, status
        FROM game_history
        WHERE (
            total_calls > 0
            AND username NOT LIKE '%Demo%'
            AND NOT (username = 'Game Reset' AND total_calls = 0)
            AND players > 0
        )
        ORDER BY date_time DESC
        LIMIT 10
        ''')
        
        meaningful_games = cursor.fetchall()
        print("\nMeaningful game records:")
        for game in meaningful_games:
            print(f"  ID:{game[0]} - {game[1]} - {game[2]} - {game[3]} players - {game[4]} calls - {game[5]} ETB stake")
        
        conn.close()
        
    except Exception as e:
        print(f"Error analyzing stats database: {e}")

def identify_synchronization_points():
    """Identify where credit deductions and stats recording should be synchronized"""
    print("\n" + "="*60)
    print("CREDIT-STATS SYNCHRONIZATION POINTS")
    print("="*60)
    
    print("\n1. CREDIT DEDUCTION POINTS:")
    print("   a) usage_tracker.end_game() - Called when game ends/resets")
    print("   b) voucher_manager.clear_game_session() - Called on app exit")
    print("   c) Enhanced reset_game methods - Called on manual reset")
    
    print("\n2. STATS RECORDING POINTS:")
    print("   a) game_state_handler.reset_game() - Records game completion")
    print("   b) stats_event_hooks.on_game_completed() - Processes game events")
    print("   c) thread_safe_db.record_game_completed() - Writes to database")
    
    print("\n3. SYNCHRONIZATION GAPS:")
    print("   ❌ Credit deduction happens in usage_tracker.end_game()")
    print("   ❌ Stats recording happens in game_state_handler.reset_game()")
    print("   ❌ These are separate code paths with different triggers")
    print("   ❌ No direct communication between credit system and stats system")
    
    print("\n4. REQUIRED INTEGRATION:")
    print("   ✅ Hook stats recording directly into usage_tracker.end_game()")
    print("   ✅ Use credit deduction as the authoritative trigger for game recording")
    print("   ✅ Ensure every credit deduction creates a corresponding game record")
    print("   ✅ Pass credit deduction data to stats system for accurate recording")

def analyze_current_mismatch():
    """Analyze the current mismatch between credits and stats"""
    print("\n" + "="*60)
    print("CURRENT MISMATCH ANALYSIS")
    print("="*60)
    
    # Get credit deduction count
    voucher_db_path = 'data/vouchers.db'
    credit_deductions = 0
    
    if os.path.exists(voucher_db_path):
        try:
            conn = sqlite3.connect(voucher_db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM usage_log WHERE credits_used > 0')
            credit_deductions = cursor.fetchone()[0]
            conn.close()
        except:
            pass
    
    # Get meaningful stats count
    stats_db_path = 'data/stats.db'
    meaningful_games = 0
    
    if os.path.exists(stats_db_path):
        try:
            conn = sqlite3.connect(stats_db_path)
            cursor = conn.cursor()
            cursor.execute('''
            SELECT COUNT(*) FROM game_history
            WHERE (
                total_calls > 0
                AND username NOT LIKE '%Demo%'
                AND NOT (username = 'Game Reset' AND total_calls = 0)
                AND players > 0
            )
            ''')
            meaningful_games = cursor.fetchone()[0]
            conn.close()
        except:
            pass
    
    print(f"Credit deductions (credits_used > 0): {credit_deductions}")
    print(f"Meaningful game records: {meaningful_games}")
    print(f"Mismatch: {abs(credit_deductions - meaningful_games)} records")
    
    if credit_deductions == meaningful_games:
        print("✅ Perfect synchronization!")
    elif credit_deductions > meaningful_games:
        print(f"❌ {credit_deductions - meaningful_games} credit deductions missing corresponding game records")
    else:
        print(f"❌ {meaningful_games - credit_deductions} game records without corresponding credit deductions")

if __name__ == '__main__':
    analyze_credit_deductions()
    analyze_stats_records()
    identify_synchronization_points()
    analyze_current_mismatch()
