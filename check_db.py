#!/usr/bin/env python3
"""
Script to check the current state of the stats database
"""
import sqlite3
import os

def check_database():
    """Check the current state of the stats database"""
    db_path = 'data/stats.db'
    
    if not os.path.exists(db_path):
        print(f'Database does not exist at: {db_path}')
        return
    
    print(f'Database exists at: {db_path}')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check tables
        cursor.execute('SELECT name FROM sqlite_master WHERE type=?', ('table',))
        tables = cursor.fetchall()
        print(f'Tables: {[t[0] for t in tables]}')
        
        # Check game_history table
        if any('game_history' in t for t in tables):
            cursor.execute('SELECT COUNT(*) FROM game_history')
            count = cursor.fetchone()[0]
            print(f'Game history records: {count}')
            
            if count > 0:
                cursor.execute('SELECT * FROM game_history ORDER BY date_time DESC LIMIT 5')
                recent_games = cursor.fetchall()
                print('Recent games:')
                for game in recent_games:
                    print(f'  {game}')
            else:
                print('No game history records found')
                
            # Check table schema
            cursor.execute('PRAGMA table_info(game_history)')
            schema = cursor.fetchall()
            print('Game history table schema:')
            for col in schema:
                print(f'  {col}')
        else:
            print('game_history table does not exist')
        
        conn.close()
        
    except Exception as e:
        print(f'Error checking database: {e}')

if __name__ == '__main__':
    check_database()
